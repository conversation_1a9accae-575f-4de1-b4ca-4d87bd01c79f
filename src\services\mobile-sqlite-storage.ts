/**
 * Mobile SQLite Storage Service for SmartBoutique
 * Uses @capacitor-community/sqlite for Android/iOS native SQLite databases
 */

import { CapacitorSQLite, SQLiteConnection, SQLiteDBConnection } from '@capacitor-community/sqlite';
import { Capacitor } from '@capacitor/core';
import { Product, User, Sale, Debt, Expense, Settings } from '../types';

export class MobileSQLiteStorageService {
  private sqlite: SQLiteConnection;
  private db: SQLiteDBConnection | null = null;
  private readonly DB_NAME = 'smartboutique.db';
  private readonly DB_VERSION = 1;
  private isInitialized = false;

  constructor() {
    this.sqlite = new SQLiteConnection(CapacitorSQLite);
  }

  /**
   * Initialize the SQLite database
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      console.log('Initializing mobile SQLite database...');

      // Check if platform supports SQLite
      if (!Capacitor.isNativePlatform()) {
        throw new Error('SQLite is only supported on native platforms (Android/iOS)');
      }

      // Create connection to database
      const retCC = await this.sqlite.checkConnectionsConsistency();
      const isConn = (await this.sqlite.isConnection(this.DB_NAME, false)).result;

      if (retCC.result && isConn) {
        this.db = await this.sqlite.retrieveConnection(this.DB_NAME, false);
      } else {
        this.db = await this.sqlite.createConnection(
          this.DB_NAME,
          false,
          'no-encryption',
          this.DB_VERSION,
          false
        );
      }

      // Open the database
      await this.db.open();

      // Create tables and indexes
      await this.createTables();
      await this.createIndexes();

      this.isInitialized = true;
      console.log('Mobile SQLite database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize mobile SQLite database:', error);
      throw error;
    }
  }

  /**
   * Create database tables
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const statements = [
      // Products table
      `CREATE TABLE IF NOT EXISTS products (
        id TEXT PRIMARY KEY,
        nom TEXT NOT NULL,
        description TEXT,
        prixAchatCDF REAL NOT NULL,
        prixAchatUSD REAL,
        prixCDF REAL NOT NULL,
        prixUSD REAL,
        beneficeUnitaireCDF REAL,
        beneficeUnitaireUSD REAL,
        codeQR TEXT,
        categorie TEXT,
        stock INTEGER,
        stockMin INTEGER,
        codeBarres TEXT,
        dateCreation TEXT,
        dateModification TEXT,
        quantiteEnStock INTEGER,
        coutAchatStockCDF REAL,
        coutAchatStockUSD REAL,
        prixParPieceCDF REAL,
        prixParPieceUSD REAL
      );`,

      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        nom TEXT NOT NULL,
        email TEXT NOT NULL UNIQUE,
        role TEXT NOT NULL,
        motDePasse TEXT NOT NULL,
        dateCreation TEXT,
        actif INTEGER DEFAULT 1
      );`,

      // Sales table
      `CREATE TABLE IF NOT EXISTS sales (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        client TEXT,
        produits TEXT NOT NULL,
        totalCDF REAL NOT NULL,
        totalUSD REAL,
        typePaiement TEXT NOT NULL,
        typeVente TEXT NOT NULL,
        vendeur TEXT NOT NULL,
        numeroRecu TEXT
      );`,

      // Debts table
      `CREATE TABLE IF NOT EXISTS debts (
        id TEXT PRIMARY KEY,
        client TEXT NOT NULL,
        montantCDF REAL NOT NULL,
        montantUSD REAL,
        dateCreation TEXT NOT NULL,
        dateEcheance TEXT,
        statut TEXT NOT NULL,
        description TEXT,
        vendeur TEXT NOT NULL
      );`,

      // Expenses table
      `CREATE TABLE IF NOT EXISTS expenses (
        id TEXT PRIMARY KEY,
        description TEXT NOT NULL,
        montantCDF REAL NOT NULL,
        montantUSD REAL,
        date TEXT NOT NULL,
        categorie TEXT NOT NULL,
        utilisateur TEXT NOT NULL,
        numeroRecu TEXT
      );`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT NOT NULL
      );`
    ];

    for (const statement of statements) {
      await this.db.execute(statement);
    }
  }

  /**
   * Create database indexes for performance
   */
  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);',
      'CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);',
      'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);',
      'CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);',
      'CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);',
      'CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);',
      'CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);',
      'CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);'
    ];

    for (const index of indexes) {
      await this.db.execute(index);
    }
  }

  /**
   * Ensure database is initialized before operations
   */
  private async ensureInitialized(): Promise<void> {
    if (!this.isInitialized) {
      await this.initialize();
    }
  }

  // Products CRUD operations
  async getProducts(): Promise<Product[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.query('SELECT * FROM products ORDER BY nom');
    return result.values as Product[];
  }

  async getProduct(id: string): Promise<Product | undefined> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.query('SELECT * FROM products WHERE id = ?', [id]);
    return result.values?.[0] as Product | undefined;
  }

  async setProducts(products: Product[]): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.beginTransaction();
    try {
      // Clear existing products
      await this.db.run('DELETE FROM products');

      // Insert new products
      for (const product of products) {
        await this.db.run(
          `INSERT INTO products (
            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,
            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,
            stockMin, codeBarres, dateCreation, dateModification
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            product.id, product.nom, product.description, product.prixAchatCDF,
            product.prixAchatUSD, product.prixCDF, product.prixUSD,
            product.beneficeUnitaireCDF, product.beneficeUnitaireUSD,
            product.codeQR, product.categorie, product.stock, product.stockMin,
            product.codeBarres, product.dateCreation, product.dateModification
          ]
        );
      }

      await this.db.commitTransaction();
    } catch (error) {
      await this.db.rollbackTransaction();
      throw error;
    }
  }

  async addProduct(product: Product): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.run(
      `INSERT INTO products (
        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,
        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,
        stockMin, codeBarres, dateCreation, dateModification
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        product.id, product.nom, product.description, product.prixAchatCDF,
        product.prixAchatUSD, product.prixCDF, product.prixUSD,
        product.beneficeUnitaireCDF, product.beneficeUnitaireUSD,
        product.codeQR, product.categorie, product.stock, product.stockMin,
        product.codeBarres, product.dateCreation, product.dateModification
      ]
    );
  }

  async updateProduct(product: Product): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.run(
      `UPDATE products SET
        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,
        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,
        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,
        dateModification = ?
      WHERE id = ?`,
      [
        product.nom, product.description, product.prixAchatCDF, product.prixAchatUSD,
        product.prixCDF, product.prixUSD, product.beneficeUnitaireCDF,
        product.beneficeUnitaireUSD, product.codeQR, product.categorie,
        product.stock, product.stockMin, product.codeBarres,
        product.dateModification, product.id
      ]
    );
  }

  async deleteProduct(id: string): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.run('DELETE FROM products WHERE id = ?', [id]);
  }

  // Users CRUD operations
  async getUsers(): Promise<User[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.query('SELECT * FROM users ORDER BY nom');
    return (result.values as any[]).map(user => ({
      ...user,
      actif: Boolean(user.actif)
    }));
  }

  async setUsers(users: User[]): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.beginTransaction();
    try {
      await this.db.run('DELETE FROM users');

      for (const user of users) {
        await this.db.run(
          'INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)',
          [user.id, user.nom, user.email, user.role, user.motDePasse, user.dateCreation, user.actif ? 1 : 0]
        );
      }

      await this.db.commitTransaction();
    } catch (error) {
      await this.db.rollbackTransaction();
      throw error;
    }
  }

  // Sales CRUD operations
  async getSales(): Promise<Sale[]> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const result = await this.db.query('SELECT * FROM sales ORDER BY date DESC');
    return (result.values as any[]).map(sale => ({
      ...sale,
      produits: JSON.parse(sale.produits),
      datevente: sale.date,
      nomClient: sale.client,
      methodePaiement: sale.typePaiement
    }));
  }

  async setSales(sales: Sale[]): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    await this.db.beginTransaction();
    try {
      await this.db.run('DELETE FROM sales');

      for (const sale of sales) {
        await this.db.run(
          'INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
          [
            sale.id, sale.datevente, sale.nomClient, JSON.stringify(sale.produits),
            sale.totalCDF, sale.totalUSD, sale.methodePaiement, sale.typeVente,
            sale.vendeur, sale.numeroRecu
          ]
        );
      }

      await this.db.commitTransaction();
    } catch (error) {
      await this.db.rollbackTransaction();
      throw error;
    }
  }

  // Close database connection
  async close(): Promise<void> {
    if (this.db) {
      await this.db.close();
      await this.sqlite.closeConnection(this.DB_NAME, false);
      this.db = null;
      this.isInitialized = false;
    }
  }

  // Get database statistics
  async getStats(): Promise<{
    products: number;
    users: number;
    sales: number;
    dbSize: number;
  }> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    const [productCount, userCount, saleCount] = await Promise.all([
      this.db.query('SELECT COUNT(*) as count FROM products'),
      this.db.query('SELECT COUNT(*) as count FROM users'),
      this.db.query('SELECT COUNT(*) as count FROM sales')
    ]);

    return {
      products: productCount.values?.[0]?.count || 0,
      users: userCount.values?.[0]?.count || 0,
      sales: saleCount.values?.[0]?.count || 0,
      dbSize: 0 // Can be calculated from database file size
    };
  }

  /**
   * Clear all data from mobile SQLite database
   */
  async clearAllData(): Promise<void> {
    await this.ensureInitialized();
    if (!this.db) throw new Error('Database not initialized');

    try {
      // Clear all tables
      await this.db.execute('DELETE FROM products');
      await this.db.execute('DELETE FROM users');
      await this.db.execute('DELETE FROM sales');
      await this.db.execute('DELETE FROM debts');
      await this.db.execute('DELETE FROM expenses');
      await this.db.execute('DELETE FROM settings');

      // Reset auto-increment counters
      await this.db.execute('DELETE FROM sqlite_sequence');

      console.log('✅ All mobile SQLite data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing mobile SQLite data:', error);
      throw error;
    }
  }
}

export const mobileSQLiteStorageService = new MobileSQLiteStorageService();
