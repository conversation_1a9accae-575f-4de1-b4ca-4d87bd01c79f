import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Grid,
  Card,
  CardContent,
  CardHeader,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,

  Tab,
  Tabs,
  Divider,
} from '@mui/material';
import {
  Download,
  Assessment,
  TrendingUp,
  PointOfSale,
  Inventory,
  AccountBalance,
  Receipt,

} from '@mui/icons-material';
import { Line, Bar, Doughnut } from 'react-chartjs-2';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';

// Types
import { Sale, Product, Debt, Expense } from '@/types';

// Utils
import { format, startOfMonth, endOfMonth, subDays, isWithinInterval, startOfDay, endOfDay } from 'date-fns';


interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`reports-tabpanel-${index}`}
      aria-labelledby={`reports-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const ReportsPage: React.FC = () => {
  const [tabValue, setTabValue] = useState(0);
  const [dateRange, setDateRange] = useState('this_month');
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [debts, setDebts] = useState<Debt[]>([]);
  const [expenses, setExpenses] = useState<Expense[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [filteredExpenses, setFilteredExpenses] = useState<Expense[]>([]);

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterDataByDateRange();
  }, [sales, expenses, dateRange, startDate, endDate]);

  const loadData = async () => {
    setSales(await adaptiveStorageService.getSales());
    setProducts(await adaptiveStorageService.getProducts());
    setDebts(await adaptiveStorageService.getDebts());
    setExpenses(await adaptiveStorageService.getExpenses());
  };

  const filterDataByDateRange = () => {
    const today = new Date();
    let start: Date;
    let end: Date;

    switch (dateRange) {
      case 'today':
        start = startOfDay(today);
        end = endOfDay(today);
        break;
      case 'this_week':
        start = subDays(today, 7);
        end = today;
        break;
      case 'this_month':
        start = startOfMonth(today);
        end = endOfMonth(today);
        break;
      case 'last_month':
        const lastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        start = startOfMonth(lastMonth);
        end = endOfMonth(lastMonth);
        break;
      case 'custom':
        if (startDate && endDate) {
          start = new Date(startDate);
          end = new Date(endDate);
        } else {
          start = startOfMonth(today);
          end = endOfMonth(today);
        }
        break;
      default:
        start = startOfMonth(today);
        end = endOfMonth(today);
    }

    const filtered = sales.filter(sale => {
      const saleDate = new Date(sale.datevente);
      return isWithinInterval(saleDate, { start, end });
    });

    const filteredExp = expenses.filter(expense => {
      const expenseDate = new Date(expense.dateDepense);
      return isWithinInterval(expenseDate, { start, end });
    });

    setFilteredSales(filtered);
    setFilteredExpenses(filteredExp);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const formatCurrency = (amount: number, currency: 'USD' | 'CDF') => {
    if (currency === 'USD') {
      return `$${amount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })}`;
    }
    return `${amount.toLocaleString('fr-FR')} CDF`;
  };

  // Helper function to format dual currency for reports (CDF primary, USD secondary)
  const formatDualCurrencyForReports = (amountCDF: number) => {
    const amountUSD = amountCDF / (settings?.tauxChangeUSDCDF || 2800);
    return {
      primary: `${amountCDF.toLocaleString('fr-FR')} CDF`,
      secondary: `$${amountUSD.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    };
  };

  // Sales Analytics
  const totalRevenue = filteredSales.reduce((sum, sale) => sum + sale.totalCDF, 0);
  const totalSalesCount = filteredSales.length;
  const averageSale = totalSalesCount > 0 ? totalRevenue / totalSalesCount : 0;

  // Expenses Analytics
  const totalExpenses = filteredExpenses.reduce((sum, expense) => sum + expense.montantCDF, 0);
  const netProfit = totalRevenue - totalExpenses;

  // Product Performance
  const productSales = filteredSales.reduce((acc: any, sale) => {
    sale.produits.forEach(item => {
      if (!acc[item.produitId]) {
        acc[item.produitId] = {
          nom: item.nomProduit,
          quantite: 0,
          revenue: 0,
        };
      }
      acc[item.produitId].quantite += item.quantite;
      acc[item.produitId].revenue += item.totalCDF;
    });
    return acc;
  }, {});

  const topProducts = Object.values(productSales)
    .sort((a: any, b: any) => b.revenue - a.revenue)
    .slice(0, 10);

  // Payment Methods
  const paymentMethods = filteredSales.reduce((acc: any, sale) => {
    acc[sale.methodePaiement] = (acc[sale.methodePaiement] || 0) + sale.totalCDF;
    return acc;
  }, {});

  // Category Performance
  const categoryPerformance = filteredSales.reduce((acc: any, sale) => {
    sale.produits.forEach(item => {
      const product = products.find(p => p.id === item.produitId);
      if (product) {
        acc[product.categorie] = (acc[product.categorie] || 0) + item.totalCDF;
      }
    });
    return acc;
  }, {});

  // Daily Sales Trend
  const dailySales = filteredSales.reduce((acc: any, sale) => {
    const date = format(new Date(sale.datevente), 'yyyy-MM-dd');
    acc[date] = (acc[date] || 0) + sale.totalCDF;
    return acc;
  }, {});

  const last30Days = Array.from({ length: 30 }, (_, i) => {
    const date = subDays(new Date(), 29 - i);
    const dateStr = format(date, 'yyyy-MM-dd');
    return {
      date: format(date, 'dd/MM'),
      revenue: dailySales[dateStr] || 0,
    };
  });

  // Charts Data
  const salesTrendData = {
    labels: last30Days.map(d => d.date),
    datasets: [
      {
        label: 'Revenus (USD)',
        data: last30Days.map(d => d.revenue),
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.2)',
        tension: 0.1,
      },
    ],
  };

  const paymentMethodData = {
    labels: ['Espèces', 'Carte', 'Mobile Money'],
    datasets: [
      {
        data: [
          paymentMethods.cash || 0,
          paymentMethods.card || 0,
          paymentMethods.mobile_money || 0,
        ],
        backgroundColor: ['#FF6384', '#36A2EB', '#FFCE56'],
      },
    ],
  };

  const categoryData = {
    labels: Object.keys(categoryPerformance),
    datasets: [
      {
        label: 'Revenus par catégorie (USD)',
        data: Object.values(categoryPerformance),
        backgroundColor: [
          '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40',
        ],
      },
    ],
  };

  const handleExportReport = () => {
    // Create CSV report content
    const periode = dateRange === 'custom' ? `${startDate} - ${endDate}` : dateRange;
    const dateGeneration = new Date().toISOString();

    // Summary section
    const summaryCSV = `Résumé du Rapport
Période,${periode}
Date de Génération,${dateGeneration}
Total Ventes,${totalSalesCount}
Chiffre d'Affaires (USD),${totalRevenue.toFixed(2)}
Total Dépenses (USD),${totalExpenses.toFixed(2)}
Bénéfice Net (USD),${netProfit.toFixed(2)}
Vente Moyenne (USD),${averageSale.toFixed(2)}
`;

    // Top products section
    const topProductsCSV = `
Top Produits les Plus Vendus
Rang,Produit,Quantité Vendue,Revenus (USD),Revenus Moyens (USD)
${topProducts.map((product: any, index) =>
      `${index + 1},${product.nom},${product.quantite},${product.revenue.toFixed(2)},${(product.revenue / product.quantite).toFixed(2)}`
    ).join('\n')}
`;

    // Payment methods section
    const paymentMethodsCSV = `
Méthodes de Paiement
Méthode,Nombre de Transactions,Montant Total (USD)
${paymentMethods.map((method: any) =>
      `${method.method},${method.count},${method.amount.toFixed(2)}`
    ).join('\n')}
`;

    // Category performance section
    const categoryPerformanceCSV = `
Performance par Catégorie
Catégorie,Revenus (USD),Nombre de Ventes
${categoryPerformance.map((cat: any) =>
      `${cat.category},${cat.revenue.toFixed(2)},${cat.sales}`
    ).join('\n')}
`;

    // Combine all sections
    const reportCSV = `SmartBoutique - Rapport d'Analyse
${summaryCSV}${topProductsCSV}${paymentMethodsCSV}${categoryPerformanceCSV}`;

    const blob = new Blob([reportCSV], { type: 'text/csv;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `SmartBoutique_Rapport_${dateRange}_${format(new Date(), 'yyyy-MM-dd')}.csv`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Rapports et Analyses
        </Typography>
        <Button
          variant="contained"
          startIcon={<Download />}
          onClick={handleExportReport}
        >
          Exporter le Rapport
        </Button>
      </Box>

      {/* Date Range Filter */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={4}>
            <FormControl fullWidth>
              <InputLabel>Période</InputLabel>
              <Select
                value={dateRange}
                label="Période"
                onChange={(e) => setDateRange(e.target.value)}
              >
                <MenuItem value="today">Aujourd'hui</MenuItem>
                <MenuItem value="this_week">Cette semaine</MenuItem>
                <MenuItem value="this_month">Ce mois</MenuItem>
                <MenuItem value="last_month">Mois dernier</MenuItem>
                <MenuItem value="custom">Personnalisée</MenuItem>
              </Select>
            </FormControl>
          </Grid>
          {dateRange === 'custom' && (
            <>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Date de début"
                  type="date"
                  value={startDate}
                  onChange={(e) => setStartDate(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
              <Grid item xs={12} md={3}>
                <TextField
                  fullWidth
                  label="Date de fin"
                  type="date"
                  value={endDate}
                  onChange={(e) => setEndDate(e.target.value)}
                  InputLabelProps={{ shrink: true }}
                />
              </Grid>
            </>
          )}
        </Grid>
      </Paper>

      {/* Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Chiffre d'Affaires
                  </Typography>
                  <Typography variant="h6" color="primary" fontWeight="medium">
                    {formatDualCurrencyForReports(totalRevenue * (settings?.tauxChangeUSDCDF || 2800)).primary}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ≈ {formatDualCurrencyForReports(totalRevenue * (settings?.tauxChangeUSDCDF || 2800)).secondary}
                  </Typography>
                </Box>
                <TrendingUp color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Nombre de Ventes
                  </Typography>
                  <Typography variant="h6" color="success.main">
                    {totalSalesCount}
                  </Typography>
                </Box>
                <PointOfSale color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Dépenses
                  </Typography>
                  <Typography variant="h6" color="error" fontWeight="medium">
                    {formatDualCurrencyForReports(totalExpenses * (settings?.tauxChangeUSDCDF || 2800)).primary}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ≈ {formatDualCurrencyForReports(totalExpenses * (settings?.tauxChangeUSDCDF || 2800)).secondary}
                  </Typography>
                </Box>
                <Receipt color="error" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Bénéfice Net
                  </Typography>
                  <Typography
                    variant="h6"
                    color={netProfit >= 0 ? 'success.main' : 'error'}
                    fontWeight="medium"
                  >
                    {formatDualCurrencyForReports(netProfit * (settings?.tauxChangeUSDCDF || 2800)).primary}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    ≈ {formatDualCurrencyForReports(netProfit * (settings?.tauxChangeUSDCDF || 2800)).secondary}
                  </Typography>
                </Box>
                <AccountBalance
                  color={netProfit >= 0 ? 'success' : 'error'}
                  sx={{ fontSize: 40 }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="reports tabs">
          <Tab label="Tendances" icon={<TrendingUp />} />
          <Tab label="Produits" icon={<Inventory />} />
          <Tab label="Analyses" icon={<Assessment />} />
        </Tabs>
      </Paper>

      {/* Trends Tab */}
      <CustomTabPanel value={tabValue} index={0}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardHeader title="Évolution des Ventes (30 derniers jours)" />
              <CardContent>
                <Line
                  data={salesTrendData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardHeader title="Méthodes de Paiement" />
              <CardContent>
                <Doughnut
                  data={paymentMethodData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'bottom' as const,
                      },
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardHeader title="Performance par Catégorie" />
              <CardContent>
                <Bar
                  data={categoryData}
                  options={{
                    responsive: true,
                    plugins: {
                      legend: {
                        position: 'top' as const,
                      },
                    },
                    scales: {
                      y: {
                        beginAtZero: true,
                      },
                    },
                  }}
                />
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </CustomTabPanel>

      {/* Products Tab */}
      <CustomTabPanel value={tabValue} index={1}>
        <Card>
          <CardHeader title="Top 10 Produits les Plus Vendus" />
          <CardContent>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Rang</TableCell>
                    <TableCell>Produit</TableCell>
                    <TableCell align="right">Quantité Vendue</TableCell>
                    <TableCell align="right">Revenus</TableCell>
                    <TableCell align="right">Revenus Moyens</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {topProducts.map((product: any, index) => (
                    <TableRow key={index}>
                      <TableCell>
                        <Chip
                          label={`#${index + 1}`}
                          color={index < 3 ? 'primary' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{product.nom}</TableCell>
                      <TableCell align="right">{product.quantite}</TableCell>
                      <TableCell align="right">
                        {formatCurrency(product.revenue, 'USD')}
                      </TableCell>
                      <TableCell align="right">
                        {formatCurrency(product.revenue / product.quantite, 'USD')}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </CustomTabPanel>

      {/* Analytics Tab */}
      <CustomTabPanel value={tabValue} index={2}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Métriques de Performance" />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Vente Moyenne
                    </Typography>
                    <Typography variant="h6">
                      {formatCurrency(averageSale, 'USD')}
                    </Typography>
                  </Grid>

                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Produits Vendus
                    </Typography>
                    <Typography variant="h6">
                      {filteredSales.reduce((sum, sale) =>
                        sum + sale.produits.reduce((pSum, p) => pSum + p.quantite, 0), 0
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={6}>
                    <Typography variant="body2" color="text.secondary">
                      Panier Moyen
                    </Typography>
                    <Typography variant="h6">
                      {totalSalesCount > 0 ?
                        (filteredSales.reduce((sum, sale) => sum + sale.produits.length, 0) / totalSalesCount).toFixed(1)
                        : '0'
                      } articles
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader title="Répartition des Revenus" />
              <CardContent>
                <Grid container spacing={2}>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Ventes Cash
                    </Typography>
                    <Typography variant="h6" color="success.main">
                      {formatCurrency(
                        filteredSales
                          .filter(s => s.typeVente === 'cash')
                          .reduce((sum, s) => sum + s.totalCDF, 0),
                        'CDF'
                      )}
                    </Typography>
                  </Grid>
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Ventes à Crédit
                    </Typography>
                    <Typography variant="h6" color="warning.main">
                      {formatCurrency(
                        filteredSales
                          .filter(s => s.typeVente === 'credit')
                          .reduce((sum, s) => sum + s.totalCDF, 0),
                        'CDF'
                      )}
                    </Typography>
                  </Grid>
                  <Divider sx={{ width: '100%', my: 1 }} />
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Créances Actives
                    </Typography>
                    <Typography variant="h6" color="error">
                      {formatCurrency(
                        debts
                          .filter(d => d.statut !== 'paid')
                          .reduce((sum, d) => sum + d.montantRestantCDF, 0),
                        'CDF'
                      )}
                    </Typography>
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12}>
            <Card>
              <CardHeader title="Analyse des Dépenses par Catégorie" />
              <CardContent>
                <TableContainer>
                  <Table>
                    <TableHead>
                      <TableRow>
                        <TableCell>Catégorie</TableCell>
                        <TableCell align="right">Nombre</TableCell>
                        <TableCell align="right">Montant Total</TableCell>
                        <TableCell align="right">Montant Moyen</TableCell>
                        <TableCell align="right">% du Total</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {Object.entries(
                        filteredExpenses.reduce((acc: any, expense) => {
                          if (!acc[expense.categorie]) {
                            acc[expense.categorie] = {
                              count: 0,
                              total: 0,
                            };
                          }
                          acc[expense.categorie].count += 1;
                          acc[expense.categorie].total += expense.montantCDF;
                          return acc;
                        }, {})
                      )
                        .sort(([,a], [,b]) => (b as any).total - (a as any).total)
                        .map(([category, data]: [string, any]) => (
                          <TableRow key={category}>
                            <TableCell>{category}</TableCell>
                            <TableCell align="right">{data.count}</TableCell>
                            <TableCell align="right">
                              {formatCurrency(data.total, 'USD')}
                            </TableCell>
                            <TableCell align="right">
                              {formatCurrency(data.total / data.count, 'USD')}
                            </TableCell>
                            <TableCell align="right">
                              {totalExpenses > 0 ? `${((data.total / totalExpenses) * 100).toFixed(1)}%` : '0%'}
                            </TableCell>
                          </TableRow>
                        ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </CustomTabPanel>
    </Box>
  );
};

export default ReportsPage;
