const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./web-C6IHjsrw.js","./mui-DD5P1k0D.js","./vendor-B_Ch-B_d.js","./utils-Bt0EYNjk.js","./charts-UhR5A4U7.js","./web-Bih2Ec3i.js"])))=>i.map(i=>d[i]);
var e,t=Object.defineProperty,n=(e,n,i)=>((e,n,i)=>n in e?t(e,n,{enumerable:!0,configurable:!0,writable:!0,value:i}):e[n]=i)(e,"symbol"!=typeof n?n+"":n,i);import{j as i,B as s,A as r,T as a,a as o,b as l,P as c,c as d,d as u,D as m,e as h,L as p,f as x,g,h as y,i as j,I as v,k as b,l as S,M as D,m as f,R as C,n as w,o as P,S as E,p as U,q as T,r as F,N as k,s as R,t as M,C as I,u as A,v as N,w as L,x as O,y as V,z as q,E as B,F as z,G as _,W as $,H as W,J as X,K as Q,O as J,Q as H,U as Y,V as K,X as G,Y as Z,Z as ee,_ as te,$ as ne,a0 as ie,a1 as se,a2 as re,a3 as ae,a4 as oe,a5 as le,a6 as ce,a7 as de,a8 as ue,a9 as me,aa as he,ab as pe,ac as xe,ad as ge,ae as ye,af as je,ag as ve,ah as be,ai as Se,aj as De,ak as fe,al as Ce,am as we,an as Pe,ao as Ee,ap as Ue,aq as Te,ar as Fe,as as ke,at as Re,au as Me,av as Ie,aw as Ae,ax as Ne,ay as Le,az as Oe,aA as Ve,aB as qe,aC as Be,aD as ze,aE as _e,aF as $e,aG as We,aH as Xe,aI as Qe,aJ as Je,aK as He,aL as Ye,aM as Ke,aN as Ge,aO as Ze,aP as et,aQ as tt,aR as nt,aS as it,aT as st,aU as rt,aV as at,aW as ot,aX as lt,aY as ct,aZ as dt,a_ as ut,a$ as mt,b0 as ht,b1 as pt,b2 as xt,b3 as gt,b4 as yt,b5 as jt,b6 as vt,b7 as bt}from"./mui-DD5P1k0D.js";import{c as St,r as Dt,R as ft,a as Ct}from"./vendor-B_Ch-B_d.js";import{b as wt,a as Pt,c as Et,d as Ut,f as Tt,s as Ft,e as kt,g as Rt,i as Mt,h as It,j as At,k as Nt,l as Lt,m as Ot}from"./utils-Bt0EYNjk.js";import{L as Vt,D as qt,B as Bt,C as zt,a as _t,b as $t,P as Wt,c as Xt,d as Qt,p as Jt,e as Ht,f as Yt,A as Kt}from"./charts-UhR5A4U7.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const n of e)if("childList"===n.type)for(const e of n.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();var Gt,Zt,en={},tn=St;
/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function nn(){return nn=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},nn.apply(this,arguments)}en.createRoot=tn.createRoot,en.hydrateRoot=tn.hydrateRoot,(Zt=Gt||(Gt={})).Pop="POP",Zt.Push="PUSH",Zt.Replace="REPLACE";const sn="popstate";function rn(e){return void 0===e&&(e={}),function(e,t,n,i){void 0===i&&(i={});let{window:s=document.defaultView,v5Compat:r=!1}=i,a=s.history,o=Gt.Pop,l=null,c=d();null==c&&(c=0,a.replaceState(nn({},a.state,{idx:c}),""));function d(){return(a.state||{idx:null}).idx}function u(){o=Gt.Pop;let e=d(),t=null==e?null:e-c;c=e,l&&l({action:o,location:x.location,delta:t})}function m(e,t){o=Gt.Push;let i=cn(x.location,e,t);n&&n(i,e),c=d()+1;let u=ln(i,c),m=x.createHref(i);try{a.pushState(u,"",m)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;s.location.assign(m)}r&&l&&l({action:o,location:x.location,delta:1})}function h(e,t){o=Gt.Replace;let i=cn(x.location,e,t);n&&n(i,e),c=d();let s=ln(i,c),u=x.createHref(i);a.replaceState(s,"",u),r&&l&&l({action:o,location:x.location,delta:0})}function p(e){let t="null"!==s.location.origin?s.location.origin:s.location.href,n="string"==typeof e?e:dn(e);return n=n.replace(/ $/,"%20"),an(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}let x={get action(){return o},get location(){return e(s,a)},listen(e){if(l)throw new Error("A history only accepts one active listener");return s.addEventListener(sn,u),l=e,()=>{s.removeEventListener(sn,u),l=null}},createHref:e=>t(s,e),createURL:p,encodeLocation(e){let t=p(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:m,replace:h,go:e=>a.go(e)};return x}(function(e,t){let{pathname:n="/",search:i="",hash:s=""}=un(e.location.hash.substr(1));return n.startsWith("/")||n.startsWith(".")||(n="/"+n),cn("",{pathname:n,search:i,hash:s},t.state&&t.state.usr||null,t.state&&t.state.key||"default")},function(e,t){let n=e.document.querySelector("base"),i="";if(n&&n.getAttribute("href")){let t=e.location.href,n=t.indexOf("#");i=-1===n?t:t.slice(0,n)}return i+"#"+("string"==typeof t?t:dn(t))},function(e,t){on("/"===e.pathname.charAt(0),"relative pathnames are not supported in hash history.push("+JSON.stringify(t)+")")},e)}function an(e,t){if(!1===e||null==e)throw new Error(t)}function on(e,t){if(!e){"undefined"!=typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function ln(e,t){return{usr:e.state,key:e.key,idx:t}}function cn(e,t,n,i){return void 0===n&&(n=null),nn({pathname:"string"==typeof e?e:e.pathname,search:"",hash:""},"string"==typeof t?un(t):t,{state:n,key:t&&t.key||i||Math.random().toString(36).substr(2,8)})}function dn(e){let{pathname:t="/",search:n="",hash:i=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),i&&"#"!==i&&(t+="#"===i.charAt(0)?i:"#"+i),t}function un(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let i=e.indexOf("?");i>=0&&(t.search=e.substr(i),e=e.substr(0,i)),e&&(t.pathname=e)}return t}var mn,hn;function pn(e,t,n){return void 0===n&&(n="/"),function(e,t,n){let i="string"==typeof t?un(t):t,s=Un(i.pathname||"/",n);if(null==s)return null;let r=xn(e);!function(e){e.sort((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every((e,n)=>e===t[n]);return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map(e=>e.childrenIndex),t.routesMeta.map(e=>e.childrenIndex)))}(r);let a=null;for(let o=0;null==a&&o<r.length;++o){let e=En(s);a=wn(r[o],e)}return a}(e,t,n)}function xn(e,t,n,i){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===i&&(i="");let s=(e,s,r)=>{let a={relativePath:void 0===r?e.path||"":r,caseSensitive:!0===e.caseSensitive,childrenIndex:s,route:e};a.relativePath.startsWith("/")&&(an(a.relativePath.startsWith(i),'Absolute route path "'+a.relativePath+'" nested under path "'+i+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),a.relativePath=a.relativePath.slice(i.length));let o=Rn([i,a.relativePath]),l=n.concat(a);e.children&&e.children.length>0&&(an(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+o+'".'),xn(e.children,t,l,o)),(null!=e.path||e.index)&&t.push({path:o,score:Cn(o,e.index),routesMeta:l})};return e.forEach((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let i of gn(e.path))s(e,t,i);else s(e,t)}),t}function gn(e){let t=e.split("/");if(0===t.length)return[];let[n,...i]=t,s=n.endsWith("?"),r=n.replace(/\?$/,"");if(0===i.length)return s?[r,""]:[r];let a=gn(i.join("/")),o=[];return o.push(...a.map(e=>""===e?r:[r,e].join("/"))),s&&o.push(...a),o.map(t=>e.startsWith("/")&&""===t?"/":t)}(hn=mn||(mn={})).data="data",hn.deferred="deferred",hn.redirect="redirect",hn.error="error";const yn=/^:[\w-]+$/,jn=3,vn=2,bn=1,Sn=10,Dn=-2,fn=e=>"*"===e;function Cn(e,t){let n=e.split("/"),i=n.length;return n.some(fn)&&(i+=Dn),t&&(i+=vn),n.filter(e=>!fn(e)).reduce((e,t)=>e+(yn.test(t)?jn:""===t?bn:Sn),i)}function wn(e,t,n){let{routesMeta:i}=e,s={},r="/",a=[];for(let o=0;o<i.length;++o){let e=i[o],n=o===i.length-1,l="/"===r?t:t.slice(r.length)||"/",c=Pn({path:e.relativePath,caseSensitive:e.caseSensitive,end:n},l),d=e.route;if(!c)return null;Object.assign(s,c.params),a.push({params:s,pathname:Rn([r,c.pathname]),pathnameBase:Mn(Rn([r,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(r=Rn([r,c.pathnameBase]))}return a}function Pn(e,t){"string"==typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,i]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);on("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let i=[],s="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(e,t,n)=>(i.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)"));e.endsWith("*")?(i.push({paramName:"*"}),s+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?s+="\\/*$":""!==e&&"/"!==e&&(s+="(?:(?=\\/|$))");let r=new RegExp(s,t?void 0:"i");return[r,i]}(e.path,e.caseSensitive,e.end),s=t.match(n);if(!s)return null;let r=s[0],a=r.replace(/(.)\/+$/,"$1"),o=s.slice(1);return{params:i.reduce((e,t,n)=>{let{paramName:i,isOptional:s}=t;if("*"===i){let e=o[n]||"";a=r.slice(0,r.length-e.length).replace(/(.)\/+$/,"$1")}const l=o[n];return e[i]=s&&!l?void 0:(l||"").replace(/%2F/g,"/"),e},{}),pathname:r,pathnameBase:a,pattern:e}}function En(e){try{return e.split("/").map(e=>decodeURIComponent(e).replace(/\//g,"%2F")).join("/")}catch(t){return on(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Un(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,i=e.charAt(n);return i&&"/"!==i?null:e.slice(n)||"/"}function Tn(e,t,n,i){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(i)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function Fn(e,t){let n=function(e){return e.filter((e,t)=>0===t||e.route.path&&e.route.path.length>0)}(e);return t?n.map((e,t)=>t===n.length-1?e.pathname:e.pathnameBase):n.map(e=>e.pathnameBase)}function kn(e,t,n,i){let s;void 0===i&&(i=!1),"string"==typeof e?s=un(e):(s=nn({},e),an(!s.pathname||!s.pathname.includes("?"),Tn("?","pathname","search",s)),an(!s.pathname||!s.pathname.includes("#"),Tn("#","pathname","hash",s)),an(!s.search||!s.search.includes("#"),Tn("#","search","hash",s)));let r,a=""===e||""===s.pathname,o=a?"/":s.pathname;if(null==o)r=n;else{let e=t.length-1;if(!i&&o.startsWith("..")){let t=o.split("/");for(;".."===t[0];)t.shift(),e-=1;s.pathname=t.join("/")}r=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:i="",hash:s=""}="string"==typeof e?un(e):e,r=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)}),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:r,search:In(i),hash:An(s)}}(s,r),c=o&&"/"!==o&&o.endsWith("/"),d=(a||"."===o)&&n.endsWith("/");return l.pathname.endsWith("/")||!c&&!d||(l.pathname+="/"),l}const Rn=e=>e.join("/").replace(/\/\/+/g,"/"),Mn=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),In=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",An=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";const Nn=["post","put","patch","delete"];new Set(Nn);const Ln=["get",...Nn];
/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
function On(){return On=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},On.apply(this,arguments)}new Set(Ln);const Vn=Dt.createContext(null),qn=Dt.createContext(null),Bn=Dt.createContext(null),zn=Dt.createContext(null),_n=Dt.createContext({outlet:null,matches:[],isDataRoute:!1}),$n=Dt.createContext(null);function Wn(){return null!=Dt.useContext(zn)}function Xn(){return Wn()||an(!1),Dt.useContext(zn).location}function Qn(e){Dt.useContext(Bn).static||Dt.useLayoutEffect(e)}function Jn(){let{isDataRoute:e}=Dt.useContext(_n);return e?function(){let{router:e}=function(){let e=Dt.useContext(Vn);return e||an(!1),e}(ti.UseNavigateStable),t=ii(ni.UseNavigateStable),n=Dt.useRef(!1);return Qn(()=>{n.current=!0}),Dt.useCallback(function(i,s){void 0===s&&(s={}),n.current&&("number"==typeof i?e.navigate(i):e.navigate(i,On({fromRouteId:t},s)))},[e,t])}():function(){Wn()||an(!1);let e=Dt.useContext(Vn),{basename:t,future:n,navigator:i}=Dt.useContext(Bn),{matches:s}=Dt.useContext(_n),{pathname:r}=Xn(),a=JSON.stringify(Fn(s,n.v7_relativeSplatPath)),o=Dt.useRef(!1);return Qn(()=>{o.current=!0}),Dt.useCallback(function(n,s){if(void 0===s&&(s={}),!o.current)return;if("number"==typeof n)return void i.go(n);let l=kn(n,JSON.parse(a),r,"path"===s.relative);null==e&&"/"!==t&&(l.pathname="/"===l.pathname?t:Rn([t,l.pathname])),(s.replace?i.replace:i.push)(l,s.state,s)},[t,i,a,r,e])}()}const Hn=Dt.createContext(null);function Yn(e,t){return function(e,t,n,i){Wn()||an(!1);let{navigator:s}=Dt.useContext(Bn),{matches:r}=Dt.useContext(_n),a=r[r.length-1],o=a?a.params:{};!a||a.pathname;let l=a?a.pathnameBase:"/";a&&a.route;let c,d=Xn();if(t){var u;let e="string"==typeof t?un(t):t;"/"===l||(null==(u=e.pathname)?void 0:u.startsWith(l))||an(!1),c=e}else c=d;let m=c.pathname||"/",h=m;if("/"!==l){let e=l.replace(/^\//,"").split("/");h="/"+m.replace(/^\//,"").split("/").slice(e.length).join("/")}let p=pn(e,{pathname:h}),x=function(e,t,n,i){var s;void 0===t&&(t=[]);void 0===n&&(n=null);void 0===i&&(i=null);if(null==e){var r;if(!n)return null;if(n.errors)e=n.matches;else{if(!(null!=(r=i)&&r.v7_partialHydration&&0===t.length&&!n.initialized&&n.matches.length>0))return null;e=n.matches}}let a=e,o=null==(s=n)?void 0:s.errors;if(null!=o){let e=a.findIndex(e=>e.route.id&&void 0!==(null==o?void 0:o[e.route.id]));e>=0||an(!1),a=a.slice(0,Math.min(a.length,e+1))}let l=!1,c=-1;if(n&&i&&i.v7_partialHydration)for(let d=0;d<a.length;d++){let e=a[d];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=d),e.route.id){let{loaderData:t,errors:i}=n,s=e.route.loader&&void 0===t[e.route.id]&&(!i||void 0===i[e.route.id]);if(e.route.lazy||s){l=!0,a=c>=0?a.slice(0,c+1):[a[0]];break}}}return a.reduceRight((e,i,s)=>{let r,d=!1,u=null,m=null;var h;n&&(r=o&&i.route.id?o[i.route.id]:void 0,u=i.route.errorElement||Gn,l&&(c<0&&0===s?(si[h="route-fallback"]||(si[h]=!0),d=!0,m=null):c===s&&(d=!0,m=i.route.hydrateFallbackElement||null)));let p=t.concat(a.slice(0,s+1)),x=()=>{let t;return t=r?u:d?m:i.route.Component?Dt.createElement(i.route.Component,null):i.route.element?i.route.element:e,Dt.createElement(ei,{match:i,routeContext:{outlet:e,matches:p,isDataRoute:null!=n},children:t})};return n&&(i.route.ErrorBoundary||i.route.errorElement||0===s)?Dt.createElement(Zn,{location:n.location,revalidation:n.revalidation,component:u,error:r,children:x(),routeContext:{outlet:null,matches:p,isDataRoute:!0}}):x()},null)}(p&&p.map(e=>Object.assign({},e,{params:Object.assign({},o,e.params),pathname:Rn([l,s.encodeLocation?s.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?l:Rn([l,s.encodeLocation?s.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])})),r,n,i);if(t&&x)return Dt.createElement(zn.Provider,{value:{location:On({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:Gt.Pop}},x);return x}(e,t)}function Kn(){let e=function(){var e;let t=Dt.useContext($n),n=function(){let e=Dt.useContext(qn);return e||an(!1),e}(),i=ii();if(void 0!==t)return t;return null==(e=n.errors)?void 0:e[i]}(),t=function(e){return null!=e&&"number"==typeof e.status&&"string"==typeof e.statusText&&"boolean"==typeof e.internal&&"data"in e}(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return Dt.createElement(Dt.Fragment,null,Dt.createElement("h2",null,"Unexpected Application Error!"),Dt.createElement("h3",{style:{fontStyle:"italic"}},t),n?Dt.createElement("pre",{style:i},n):null,null)}const Gn=Dt.createElement(Kn,null);class Zn extends Dt.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?Dt.createElement(_n.Provider,{value:this.props.routeContext},Dt.createElement($n.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function ei(e){let{routeContext:t,match:n,children:i}=e,s=Dt.useContext(Vn);return s&&s.static&&s.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(s.staticContext._deepestRenderedBoundaryId=n.route.id),Dt.createElement(_n.Provider,{value:t},i)}var ti=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(ti||{}),ni=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(ni||{});function ii(e){let t=function(){let e=Dt.useContext(_n);return e||an(!1),e}(),n=t.matches[t.matches.length-1];return n.route.id||an(!1),n.route.id}const si={};function ri(e){let{to:t,replace:n,state:i,relative:s}=e;Wn()||an(!1);let{future:r,static:a}=Dt.useContext(Bn),{matches:o}=Dt.useContext(_n),{pathname:l}=Xn(),c=Jn(),d=kn(t,Fn(o,r.v7_relativeSplatPath),l,"path"===s),u=JSON.stringify(d);return Dt.useEffect(()=>c(JSON.parse(u),{replace:n,state:i,relative:s}),[c,u,s,n,i]),null}function ai(e){return function(e){let t=Dt.useContext(_n).outlet;return t?Dt.createElement(Hn.Provider,{value:e},t):t}(e.context)}function oi(e){an(!1)}function li(e){let{basename:t="/",children:n=null,location:i,navigationType:s=Gt.Pop,navigator:r,static:a=!1,future:o}=e;Wn()&&an(!1);let l=t.replace(/^\/*/,"/"),c=Dt.useMemo(()=>({basename:l,navigator:r,static:a,future:On({v7_relativeSplatPath:!1},o)}),[l,o,r,a]);"string"==typeof i&&(i=un(i));let{pathname:d="/",search:u="",hash:m="",state:h=null,key:p="default"}=i,x=Dt.useMemo(()=>{let e=Un(d,l);return null==e?null:{location:{pathname:e,search:u,hash:m,state:h,key:p},navigationType:s}},[l,d,u,m,h,p,s]);return null==x?null:Dt.createElement(Bn.Provider,{value:c},Dt.createElement(zn.Provider,{children:n,value:x}))}function ci(e){let{children:t,location:n}=e;return Yn(di(t),n)}function di(e,t){void 0===t&&(t=[]);let n=[];return Dt.Children.forEach(e,(e,i)=>{if(!Dt.isValidElement(e))return;let s=[...t,i];if(e.type===Dt.Fragment)return void n.push.apply(n,di(e.props.children,s));e.type!==oi&&an(!1),e.props.index&&e.props.children&&an(!1);let r={id:e.props.id||s.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(r.children=di(e.props.children,s)),n.push(r)}),n}
/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */new Promise(()=>{});try{window.__reactRouterVersion="6"}catch(Zs){}const ui=ft.startTransition;function mi(e){let{basename:t,children:n,future:i,window:s}=e,r=Dt.useRef();null==r.current&&(r.current=rn({window:s,v5Compat:!0}));let a=r.current,[o,l]=Dt.useState({action:a.action,location:a.location}),{v7_startTransition:c}=i||{},d=Dt.useCallback(e=>{c&&ui?ui(()=>l(e)):l(e)},[l,c]);return Dt.useLayoutEffect(()=>a.listen(d),[a,d]),Dt.useEffect(()=>{return null==(e=i)||e.v7_startTransition,void(null==e||e.v7_relativeSplatPath);var e},[i]),Dt.createElement(li,{basename:t,children:n,location:o.location,navigationType:o.action,navigator:a,future:i})}var hi,pi,xi,gi;(pi=hi||(hi={})).UseScrollRestoration="useScrollRestoration",pi.UseSubmit="useSubmit",pi.UseSubmitFetcher="useSubmitFetcher",pi.UseFetcher="useFetcher",pi.useViewTransitionState="useViewTransitionState",(gi=xi||(xi={})).UseFetcher="useFetcher",gi.UseFetchers="useFetchers",gi.UseScrollRestoration="useScrollRestoration";const yi={components:{MuiBreadcrumbs:{defaultProps:{expandText:"Montrer le chemin"}},MuiTablePagination:{defaultProps:{getItemAriaLabel:e=>"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente",labelRowsPerPage:"Lignes par page :",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}–${t} sur ${-1!==n?n:`plus que ${t}`}`}},MuiRating:{defaultProps:{getLabelText:e=>`${e} Etoile${1!==e?"s":""}`,emptyLabelText:"Vide"}},MuiAutocomplete:{defaultProps:{clearText:"Vider",closeText:"Fermer",loadingText:"Chargement…",noOptionsText:"Pas de résultats",openText:"Ouvrir"}},MuiAlert:{defaultProps:{closeText:"Fermer"}},MuiPagination:{defaultProps:{"aria-label":"navigation de pagination",getItemAriaLabel:(e,t,n)=>"page"===e?`${n?"":"Aller à la "}page ${t}`:"first"===e?"Aller à la première page":"last"===e?"Aller à la dernière page":"next"===e?"Aller à la page suivante":"Aller à la page précédente"}}}},ji={},vi=function(e,t,n){let i=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),s=document.querySelector("meta[property=csp-nonce]"),r=(null==s?void 0:s.nonce)||(null==s?void 0:s.getAttribute("nonce"));i=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,n),t in ji)return;ji[t]=!0;const i=t.endsWith(".css"),s=i?'[rel="stylesheet"]':"";if(!!n)for(let n=e.length-1;n>=0;n--){const s=e[n];if(s.href===t&&(!i||"stylesheet"===s.rel))return}else if(document.querySelector(`link[href="${t}"]${s}`))return;const a=document.createElement("link");return a.rel=i?"stylesheet":"modulepreload",i||(a.as="script"),a.crossOrigin="",a.href=t,r&&a.setAttribute("nonce",r),document.head.appendChild(a),i?new Promise((e,n)=>{a.addEventListener("load",e),a.addEventListener("error",()=>n(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function s(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return i.then(t=>{for(const e of t||[])"rejected"===e.status&&s(e.reason);return e().catch(s)})};
/*! Capacitor: https://capacitorjs.com/ - MIT License */
var bi,Si;(Si=bi||(bi={})).Unimplemented="UNIMPLEMENTED",Si.Unavailable="UNAVAILABLE";class Di extends Error{constructor(e,t,n){super(e),this.message=e,this.code=t,this.data=n}}const fi=e=>{const t=e.CapacitorCustomPlatform||null,n=e.Capacitor||{},i=n.Plugins=n.Plugins||{},s=()=>null!==t?t.name:(e=>{var t,n;return(null==e?void 0:e.androidBridge)?"android":(null===(n=null===(t=null==e?void 0:e.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===n?void 0:n.bridge)?"ios":"web"})(e),r=e=>{var t;return null===(t=n.PluginHeaders)||void 0===t?void 0:t.find(t=>t.name===e)},a=new Map;return n.convertFileSrc||(n.convertFileSrc=e=>e),n.getPlatform=s,n.handleError=t=>e.console.error(t),n.isNativePlatform=()=>"web"!==s(),n.isPluginAvailable=e=>{const t=a.get(e);return!!(null==t?void 0:t.platforms.has(s()))||!!r(e)},n.registerPlugin=(e,o={})=>{const l=a.get(e);if(l)return console.warn(`Capacitor plugin "${e}" already registered. Cannot register plugins twice.`),l.proxy;const c=s(),d=r(e);let u;const m=i=>{let s;const r=(...r)=>{const a=(async()=>(!u&&c in o?u=u="function"==typeof o[c]?await o[c]():o[c]:null!==t&&!u&&"web"in o&&(u=u="function"==typeof o.web?await o.web():o.web),u))().then(t=>{const a=((t,i)=>{var s,r;if(!d){if(t)return null===(r=t[i])||void 0===r?void 0:r.bind(t);throw new Di(`"${e}" plugin is not implemented on ${c}`,bi.Unimplemented)}{const r=null==d?void 0:d.methods.find(e=>i===e.name);if(r)return"promise"===r.rtype?t=>n.nativePromise(e,i.toString(),t):(t,s)=>n.nativeCallback(e,i.toString(),t,s);if(t)return null===(s=t[i])||void 0===s?void 0:s.bind(t)}})(t,i);if(a){const e=a(...r);return s=null==e?void 0:e.remove,e}throw new Di(`"${e}.${i}()" is not implemented on ${c}`,bi.Unimplemented)});return"addListener"===i&&(a.remove=async()=>s()),a};return r.toString=()=>`${i.toString()}() { [capacitor code] }`,Object.defineProperty(r,"name",{value:i,writable:!1,configurable:!1}),r},h=m("addListener"),p=m("removeListener"),x=(e,t)=>{const n=h({eventName:e},t),i=async()=>{const i=await n;p({eventName:e,callbackId:i},t)},s=new Promise(e=>n.then(()=>e({remove:i})));return s.remove=async()=>{console.warn("Using addListener() without 'await' is deprecated."),await i()},s},g=new Proxy({},{get(e,t){switch(t){case"$$typeof":return;case"toJSON":return()=>({});case"addListener":return d?x:h;case"removeListener":return p;default:return m(t)}}});return i[e]=g,a.set(e,{name:e,proxy:g,platforms:new Set([...Object.keys(o),...d?[c]:[]])}),g},n.Exception=Di,n.DEBUG=!!n.DEBUG,n.isLoggingEnabled=!!n.isLoggingEnabled,n},Ci=(e=>e.Capacitor=fi(e))("undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:"undefined"!=typeof global?global:{}),wi=Ci.registerPlugin;class Pi{constructor(){this.listeners={},this.retainedEventArguments={},this.windowListeners={}}addListener(e,t){let n=!1;this.listeners[e]||(this.listeners[e]=[],n=!0),this.listeners[e].push(t);const i=this.windowListeners[e];i&&!i.registered&&this.addWindowListener(i),n&&this.sendRetainedArgumentsForEvent(e);return Promise.resolve({remove:async()=>this.removeListener(e,t)})}async removeAllListeners(){this.listeners={};for(const e in this.windowListeners)this.removeWindowListener(this.windowListeners[e]);this.windowListeners={}}notifyListeners(e,t,n){const i=this.listeners[e];if(i)i.forEach(e=>e(t));else if(n){let n=this.retainedEventArguments[e];n||(n=[]),n.push(t),this.retainedEventArguments[e]=n}}hasListeners(e){var t;return!!(null===(t=this.listeners[e])||void 0===t?void 0:t.length)}registerWindowListener(e,t){this.windowListeners[t]={registered:!1,windowEventName:e,pluginEventName:t,handler:e=>{this.notifyListeners(t,e)}}}unimplemented(e="not implemented"){return new Ci.Exception(e,bi.Unimplemented)}unavailable(e="not available"){return new Ci.Exception(e,bi.Unavailable)}async removeListener(e,t){const n=this.listeners[e];if(!n)return;const i=n.indexOf(t);this.listeners[e].splice(i,1),this.listeners[e].length||this.removeWindowListener(this.windowListeners[e])}addWindowListener(e){window.addEventListener(e.windowEventName,e.handler),e.registered=!0}removeWindowListener(e){e&&(window.removeEventListener(e.windowEventName,e.handler),e.registered=!1)}sendRetainedArgumentsForEvent(e){const t=this.retainedEventArguments[e];t&&(delete this.retainedEventArguments[e],t.forEach(t=>{this.notifyListeners(e,t)}))}}const Ei=e=>encodeURIComponent(e).replace(/%(2[346B]|5E|60|7C)/g,decodeURIComponent).replace(/[()]/g,escape),Ui=e=>e.replace(/(%[\dA-F]{2})+/gi,decodeURIComponent);class Ti extends Pi{async getCookies(){const e=document.cookie,t={};return e.split(";").forEach(e=>{if(e.length<=0)return;let[n,i]=e.replace(/=/,"CAP_COOKIE").split("CAP_COOKIE");n=Ui(n).trim(),i=Ui(i).trim(),t[n]=i}),t}async setCookie(e){try{const t=Ei(e.key),n=Ei(e.value),i=`; expires=${(e.expires||"").replace("expires=","")}`,s=(e.path||"/").replace("path=",""),r=null!=e.url&&e.url.length>0?`domain=${e.url}`:"";document.cookie=`${t}=${n||""}${i}; path=${s}; ${r};`}catch(t){return Promise.reject(t)}}async deleteCookie(e){try{document.cookie=`${e.key}=; Max-Age=0`}catch(t){return Promise.reject(t)}}async clearCookies(){try{const e=document.cookie.split(";")||[];for(const t of e)document.cookie=t.replace(/^ +/,"").replace(/=.*/,`=;expires=${(new Date).toUTCString()};path=/`)}catch(e){return Promise.reject(e)}}async clearAllCookies(){try{await this.clearCookies()}catch(e){return Promise.reject(e)}}}wi("CapacitorCookies",{web:()=>new Ti});const Fi=(e,t={})=>{const n=Object.assign({method:e.method||"GET",headers:e.headers},t),i=((e={})=>{const t=Object.keys(e);return Object.keys(e).map(e=>e.toLocaleLowerCase()).reduce((n,i,s)=>(n[i]=e[t[s]],n),{})})(e.headers)["content-type"]||"";if("string"==typeof e.data)n.body=e.data;else if(i.includes("application/x-www-form-urlencoded")){const t=new URLSearchParams;for(const[n,i]of Object.entries(e.data||{}))t.set(n,i);n.body=t.toString()}else if(i.includes("multipart/form-data")||e.data instanceof FormData){const t=new FormData;if(e.data instanceof FormData)e.data.forEach((e,n)=>{t.append(n,e)});else for(const n of Object.keys(e.data))t.append(n,e.data[n]);n.body=t;const i=new Headers(n.headers);i.delete("content-type"),n.headers=i}else(i.includes("application/json")||"object"==typeof e.data)&&(n.body=JSON.stringify(e.data));return n};class ki extends Pi{async request(e){const t=Fi(e,e.webFetchExtra),n=((e,t=!0)=>e?Object.entries(e).reduce((e,n)=>{const[i,s]=n;let r,a;return Array.isArray(s)?(a="",s.forEach(e=>{r=t?encodeURIComponent(e):e,a+=`${i}=${r}&`}),a.slice(0,-1)):(r=t?encodeURIComponent(s):s,a=`${i}=${r}`),`${e}&${a}`},"").substr(1):null)(e.params,e.shouldEncodeUrlParams),i=n?`${e.url}?${n}`:e.url,s=await fetch(i,t),r=s.headers.get("content-type")||"";let a,o,{responseType:l="text"}=s.ok?e:{};switch(r.includes("application/json")&&(l="json"),l){case"arraybuffer":case"blob":o=await s.blob(),a=await(async e=>new Promise((t,n)=>{const i=new FileReader;i.onload=()=>{const e=i.result;t(e.indexOf(",")>=0?e.split(",")[1]:e)},i.onerror=e=>n(e),i.readAsDataURL(e)}))(o);break;case"json":a=await s.json();break;default:a=await s.text()}const c={};return s.headers.forEach((e,t)=>{c[t]=e}),{data:a,headers:c,status:s.status,url:s.url}}async get(e){return this.request(Object.assign(Object.assign({},e),{method:"GET"}))}async post(e){return this.request(Object.assign(Object.assign({},e),{method:"POST"}))}async put(e){return this.request(Object.assign(Object.assign({},e),{method:"PUT"}))}async patch(e){return this.request(Object.assign(Object.assign({},e),{method:"PATCH"}))}async delete(e){return this.request(Object.assign(Object.assign({},e),{method:"DELETE"}))}}wi("CapacitorHttp",{web:()=>new ki});const Ri=()=>{const e=Ci.getPlatform();return{isMobile:Ci.isNativePlatform(),isDesktop:!Ci.isNativePlatform(),isAndroid:"android"===e,isIOS:"ios"===e,isWeb:"web"===e,platform:e}},Mi=()=>Ci.isNativePlatform();class Ii{static arrayToCSV(e,t){if(!e||0===e.length)return t.map(e=>e.header).join(",")+"\n";return[t.map(e=>e.header).join(","),...e.map(e=>t.map(t=>{let n=e[t.key];return null==n?n="":"date"===t.type&&n?n=new Date(n).toISOString().split("T")[0]:"boolean"===t.type?n=n?"Oui":"Non":"object"==typeof n&&(n=JSON.stringify(n)),n=String(n),(n.includes(",")||n.includes('"')||n.includes("\n"))&&(n='"'+n.replace(/"/g,'""')+'"'),n}).join(","))].join("\n")}static csvToArray(e,t){if(!e||""===e.trim())return[];const n=this.parseCSVLines(e);if(n.length<=1)return[];return n.slice(1).map((e,n)=>{const i=e,s={};return t.forEach((e,t)=>{let n=i[t]||"";"number"===e.type?n=""===n?0:parseFloat(n)||0:"boolean"===e.type?n="oui"===n.toLowerCase()||"true"===n.toLowerCase()||"1"===n:"date"===e.type&&n?n=new Date(n).toISOString():e.key.includes("prix")&&"string"==typeof n&&(n=parseFloat(n)||0),s[e.key]=n}),s.id||(s.id=String(n+1)),s})}static parseCSVLines(e){const t=[],n=e.split("\n");for(const i of n){if(""===i.trim())continue;const e=[];let n="",s=!1,r=0;for(;r<i.length;){const t=i[r];'"'===t?s&&'"'===i[r+1]?(n+='"',r+=2):(s=!s,r++):","!==t||s?(n+=t,r++):(e.push(n),n="",r++)}e.push(n),t.push(e)}return t}static validateCSVData(e,t){const n=[];return e.forEach((e,i)=>{t.forEach(t=>{!t.required||void 0!==e[t.key]&&null!==e[t.key]&&""!==e[t.key]||n.push(`Ligne ${i+2}: Le champ "${t.header}" est requis`),"number"===t.type&&void 0!==e[t.key]&&isNaN(Number(e[t.key]))&&n.push(`Ligne ${i+2}: Le champ "${t.header}" doit être un nombre`)})}),{isValid:0===n.length,errors:n}}static generateTemplate(e){const t=[{}];return e.forEach(e=>{switch(e.type){case"string":t[0][e.key]="Exemple";break;case"number":t[0][e.key]=100;break;case"boolean":t[0][e.key]=!0;break;case"date":t[0][e.key]=(new Date).toISOString()}}),this.arrayToCSV(t,e)}}const Ai=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom du Produit",type:"string",required:!0},{key:"description",header:"Description",type:"string"},{key:"prixAchatCDF",header:"Prix d'achat CDF",type:"number",required:!0},{key:"prixAchatUSD",header:"Prix d'achat USD",type:"number"},{key:"prixCDF",header:"Prix de vente CDF",type:"number",required:!0},{key:"prixUSD",header:"Prix de vente USD",type:"number"},{key:"beneficeUnitaireCDF",header:"Bénéfice unitaire CDF",type:"number"},{key:"beneficeUnitaireUSD",header:"Bénéfice unitaire USD",type:"number"},{key:"codeQR",header:"Code QR",type:"string"},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"stock",header:"Stock",type:"number",required:!0},{key:"stockMin",header:"Stock Minimum",type:"number"},{key:"codeBarres",header:"Code Barres",type:"string"},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"dateModification",header:"Date de Modification",type:"date"}],Ni=[{key:"id",header:"ID",type:"string",required:!0},{key:"nom",header:"Nom",type:"string",required:!0},{key:"email",header:"Email",type:"string",required:!0},{key:"role",header:"Rôle",type:"string",required:!0},{key:"motDePasse",header:"Mot de Passe",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date"},{key:"actif",header:"Actif",type:"boolean"}],Li=[{key:"id",header:"ID",type:"string",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"client",header:"Client",type:"string"},{key:"produits",header:"Produits (JSON)",type:"string",required:!0},{key:"totalCDF",header:"Total CDF",type:"number",required:!0},{key:"totalUSD",header:"Total USD",type:"number",required:!0},{key:"typePaiement",header:"Type de Paiement",type:"string",required:!0},{key:"typeVente",header:"Type de Vente",type:"string",required:!0},{key:"vendeur",header:"Vendeur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],Oi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomClient",header:"Client",type:"string",required:!0},{key:"telephoneClient",header:"Téléphone",type:"string"},{key:"adresseClient",header:"Adresse",type:"string"},{key:"montantTotalCDF",header:"Montant Total CDF",type:"number",required:!0},{key:"montantTotalUSD",header:"Montant Total USD",type:"number"},{key:"montantPayeCDF",header:"Montant Payé CDF",type:"number",required:!0},{key:"montantPayeUSD",header:"Montant Payé USD",type:"number"},{key:"montantRestantCDF",header:"Montant Restant CDF",type:"number",required:!0},{key:"montantRestantUSD",header:"Montant Restant USD",type:"number"},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateEcheance",header:"Date d'Échéance",type:"date"},{key:"statut",header:"Statut",type:"string",required:!0},{key:"statutPaiement",header:"Statut de Paiement",type:"string",required:!0},{key:"venteId",header:"ID de Vente",type:"string"},{key:"paiements",header:"Paiements",type:"string"},{key:"notes",header:"Notes",type:"string"}],Vi=[{key:"id",header:"ID",type:"string",required:!0},{key:"description",header:"Description",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number",required:!0},{key:"date",header:"Date",type:"date",required:!0},{key:"categorie",header:"Catégorie",type:"string",required:!0},{key:"utilisateur",header:"Utilisateur",type:"string",required:!0},{key:"numeroRecu",header:"Numéro de Reçu",type:"string"}],qi=[{key:"id",header:"ID",type:"string",required:!0},{key:"nomEmploye",header:"Nom Employé",type:"string",required:!0},{key:"montantCDF",header:"Montant CDF",type:"number",required:!0},{key:"montantUSD",header:"Montant USD",type:"number"},{key:"datePaiement",header:"Date de Paiement",type:"date",required:!0},{key:"methodePaiement",header:"Méthode de Paiement",type:"string",required:!0},{key:"notes",header:"Notes",type:"string"},{key:"creePar",header:"Créé par",type:"string",required:!0},{key:"dateCreation",header:"Date de Création",type:"date",required:!0},{key:"dateModification",header:"Date de Modification",type:"date"}],Bi=[{key:"cle",header:"Clé",type:"string",required:!0},{key:"valeur",header:"Valeur",type:"string",required:!0},{key:"type",header:"Type",type:"string",required:!0},{key:"description",header:"Description",type:"string"}];function zi(e){var t,n;const i=[];return i.push({cle:"tauxChangeUSDCDF",valeur:(null==(t=e.tauxChangeUSDCDF)?void 0:t.toString())||"2800",type:"number",description:"Taux de change USD vers CDF"}),i.push({cle:"seuilStockBas",valeur:(null==(n=e.seuilStockBas)?void 0:n.toString())||"10",type:"number",description:"Seuil de stock bas"}),e.categories&&Array.isArray(e.categories)&&i.push({cle:"categories",valeur:JSON.stringify(e.categories),type:"json",description:"Catégories de produits"}),e.entreprise&&i.push({cle:"entreprise",valeur:JSON.stringify(e.entreprise),type:"json",description:"Informations de l'entreprise"}),e.impression&&i.push({cle:"impression",valeur:JSON.stringify(e.impression),type:"json",description:"Paramètres d'impression des reçus"}),i}const _i=new class{constructor(){n(this,"prefix","smartboutique_csv_")}getKey(e){return`${this.prefix}${e}`}setCSV(e,t,n){try{const i=Ii.arrayToCSV(t,n);localStorage.setItem(this.getKey(e),i)}catch(i){console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i)}}getCSV(e,t,n=[]){try{const i=localStorage.getItem(this.getKey(e));return i?Ii.csvToArray(i,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}set(e,t){try{const n=JSON.stringify(t);localStorage.setItem(this.getKey(e),n)}catch(n){console.error("Erreur lors de la sauvegarde:",n)}}get(e,t){try{const n=localStorage.getItem(this.getKey(e));return null===n?t:JSON.parse(n)}catch(n){return console.error("Erreur lors de la lecture:",n),t}}remove(e){localStorage.removeItem(this.getKey(e))}clear(){Object.keys(localStorage).forEach(e=>{e.startsWith(this.prefix)&&localStorage.removeItem(e)})}getUsers(){return this.getCSV("users",Ni,[])}setUsers(e){this.setCSV("users",e,Ni)}getProducts(){return this.getCSV("products",Ai,[]).map(e=>{!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,e.beneficeUnitaireCDF=e.beneficeUnitaireCDF?Number(e.beneficeUnitaireCDF):void 0,e.beneficeUnitaireUSD=e.beneficeUnitaireUSD?Number(e.beneficeUnitaireUSD):void 0,e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}setProducts(e){this.setCSV("products",e,Ai)}getSales(){try{const e=this.getCSV("sales",Li,[]);return e.filter(e=>e&&"object"==typeof e).map(e=>{const t={...e,datevente:e.date||e.datevente||(new Date).toISOString(),methodePaiement:e.typePaiement||e.methodePaiement||"cash",nomClient:e.client||e.nomClient||"Client"};t.totalCDF=Number(t.totalCDF)||0,t.totalUSD=t.totalUSD?Number(t.totalUSD):void 0;let n=t.produits||[];if("string"==typeof n)try{n=JSON.parse(n)}catch(i){console.warn("Error parsing produits JSON:",i),n=[]}return Array.isArray(n)?t.produits=n.map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})):t.produits=[],t})}catch(e){return console.error("Error getting sales data:",e),[]}}setSales(e){const t=e.map(e=>({...e,date:e.datevente||e.date,typePaiement:e.methodePaiement||e.typePaiement,client:e.nomClient||e.client,produits:"string"==typeof e.produits?e.produits:JSON.stringify(e.produits||[])}));this.setCSV("sales",t,Li)}getDebts(){return this.getCSV("debts",Oi,[]).map(e=>{e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0;let t=e.paiements||[];if("string"==typeof t)try{t=JSON.parse(t)}catch(n){console.warn("Error parsing paiements JSON:",n),t=[]}return Array.isArray(t)?e.paiements=t.map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})):e.paiements=[],e})}setDebts(e){this.setCSV("debts",e,Oi)}getDettes(){return this.getDebts()}setDettes(e){this.setDebts(e)}getCreances(){return this.getDebts()}setCreances(e){this.setDebts(e)}getExpenses(){return this.getCSV("expenses",Vi,[])}setExpenses(e){this.setCSV("expenses",e,Vi)}getEmployeePayments(){return this.getCSV("employee_payments",qi,[])}setEmployeePayments(e){this.setCSV("employee_payments",e,qi)}addEmployeePayment(e){const t=this.getEmployeePayments();t.push(e),this.setEmployeePayments(t)}updateEmployeePayment(e){const t=this.getEmployeePayments(),n=t.findIndex(t=>t.id===e.id);-1!==n&&(t[n]=e,this.setEmployeePayments(t))}deleteEmployeePayment(e){const t=this.getEmployeePayments().filter(t=>t.id!==e);this.setEmployeePayments(t)}getSettings(){return this.get("settings",{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+243 000 000 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}})}setSettings(e){this.set("settings",e)}getCurrentUser(){return this.get("currentUser",null)}setCurrentUser(e){this.set("currentUser",e)}initializeDefaultData(){if(0===this.getUsers().length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];this.setUsers(e)}0===this.getProducts().length&&this.initializeProductCatalog();0===this.getSales().length&&this.initializeSampleSales();0===this.getDebts().length&&this.initializeSampleDebts()}clearAllData(){localStorage.clear(),console.log("All localStorage data cleared")}initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixAchatCDF:168e4,prixAchatUSD:600,prixCDF:224e4,prixUSD:800,beneficeUnitaireCDF:56e4,beneficeUnitaireUSD:200,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixAchatCDF:7e4,prixAchatUSD:25,prixCDF:98e3,prixUSD:35,beneficeUnitaireCDF:28e3,beneficeUnitaireUSD:10,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixAchatCDF:25200,prixAchatUSD:9,prixCDF:33600,prixUSD:12,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixAchatCDF:6300,prixAchatUSD:2.25,prixCDF:8400,prixUSD:3,beneficeUnitaireCDF:2100,beneficeUnitaireUSD:.75,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixAchatCDF:33600,prixAchatUSD:12,prixCDF:42e3,prixUSD:15,beneficeUnitaireCDF:8400,beneficeUnitaireUSD:3,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];this.setProducts(e)}initializeSampleSales(){const e=new Date,t=new Date(e.getTime()-864e5),n=new Date(e.getTime()-1728e5),i=[{id:"1",datevente:t.toISOString(),nomClient:"Jean Mukendi",telephoneClient:"+243 900 000 001",produits:[{produitId:"1",nomProduit:"iPhone 15",quantite:1,prixUnitaireCDF:224e4,prixUnitaireUSD:800,totalCDF:224e4,totalUSD:800}],totalCDF:224e4,totalUSD:800,methodePaiement:"cash",typeVente:"cash",vendeur:"Super Admin",notes:"Vente comptant"},{id:"2",datevente:t.toISOString(),nomClient:"Marie Kabila",telephoneClient:"+243 900 000 002",produits:[{produitId:"2",nomProduit:"T-shirt Nike",quantite:2,prixUnitaireCDF:98e3,prixUnitaireUSD:35,totalCDF:196e3,totalUSD:70},{produitId:"4",nomProduit:"Sucre",quantite:3,prixUnitaireCDF:8400,prixUnitaireUSD:3,totalCDF:25200,totalUSD:9}],totalCDF:221200,totalUSD:79,methodePaiement:"mobile_money",typeVente:"cash",vendeur:"Gestionnaire",notes:"Paiement mobile money"},{id:"3",datevente:n.toISOString(),nomClient:"Paul Tshisekedi",telephoneClient:"+243 900 000 003",produits:[{produitId:"5",nomProduit:"Riz",quantite:2,prixUnitaireCDF:42e3,prixUnitaireUSD:15,totalCDF:84e3,totalUSD:30}],totalCDF:84e3,totalUSD:30,methodePaiement:"card",typeVente:"cash",vendeur:"Employé",notes:"Paiement par carte"}];this.setSales(i)}initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),s=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:s.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];this.setDebts(r)}exportData(){const e=this.getUsers(),t=this.getProducts(),n=this.getSales(),i=this.getDebts(),s=this.getExpenses(),r=this.getEmployeePayments(),a=this.getSettings(),o={exportDate:(new Date).toISOString(),products:Ii.arrayToCSV(t,Ai),users:Ii.arrayToCSV(e,Ni),sales:Ii.arrayToCSV(n,Li),debts:Ii.arrayToCSV(i,Oi),expenses:Ii.arrayToCSV(s,Vi),employeePayments:Ii.arrayToCSV(r,qi),settings:Ii.arrayToCSV(zi(a),Bi)};return{csvData:`SmartBoutique - Sauvegarde Complète (Desktop)\nDate d'exportation: ${o.exportDate}\n\n=== PRODUITS ===\n${o.products}\n\n=== UTILISATEURS ===\n${o.users}\n\n=== VENTES ===\n${o.sales}\n\n=== DETTES ===\n${o.debts}\n\n=== DÉPENSES ===\n${o.expenses}\n\n=== PAIEMENTS EMPLOYÉS ===\n${o.employeePayments}\n\n=== PARAMÈTRES ===\n${o.settings}\n`,exportDate:o.exportDate}}importData(e){try{return e.csvData&&"string"==typeof e.csvData?this.importFromCSVBackup(e.csvData):(e.users&&this.setUsers(e.users),e.products&&this.setProducts(e.products),e.sales&&this.setSales(e.sales),e.debts&&this.setDebts(e.debts),e.expenses&&this.setExpenses(e.expenses),e.employeePayments&&this.setEmployeePayments(e.employeePayments),e.settings&&this.setSettings(e.settings),!0)}catch(t){return console.error("Erreur lors de l'importation:",t),!1}}importFromCSVBackup(e){try{const t=this.parseCSVBackup(e);if(t.products){const e=Ii.csvToArray(t.products,Ai);this.setProducts(e)}if(t.users){const e=Ii.csvToArray(t.users,Ni);this.setUsers(e)}if(t.sales){const e=Ii.csvToArray(t.sales,Li);this.setSales(e)}if(t.debts){const e=Ii.csvToArray(t.debts,Oi);this.setDebts(e)}if(t.expenses){const e=Ii.csvToArray(t.expenses,Vi);this.setExpenses(e)}if(t.employeePayments){const e=Ii.csvToArray(t.employeePayments,qi);this.setEmployeePayments(e)}if(t.settings){const e=function(e){const t={};return e.forEach(e=>{const n=e.cle;let i=e.valeur;switch(e.type){case"number":i=parseFloat(i)||0;break;case"boolean":i="true"===i||"1"===i||"Oui"===i;break;case"json":try{i=JSON.parse(i)}catch(s){console.error(`Erreur lors du parsing JSON pour ${n}:`,s),i=null}}t[n]=i}),t}(Ii.csvToArray(t.settings,Bi));this.setSettings(e)}return!0}catch(t){return console.error("Erreur lors de l'importation CSV:",t),!1}}parseCSVBackup(e){const t={},n=e.split("\n");let i="",s=[];for(const r of n)if(r.startsWith("=== ")&&r.endsWith(" ===")){i&&s.length>0&&(t[i]=s.join("\n"));switch(r.replace(/=== | ===/g,"").toLowerCase()){case"produits":i="products";break;case"utilisateurs":i="users";break;case"ventes":i="sales";break;case"dettes":i="debts";break;case"dépenses":i="expenses";break;case"paiements employés":i="employeePayments";break;case"paramètres":i="settings";break;default:i=""}s=[]}else i&&""!==r.trim()&&s.push(r);return i&&s.length>0&&(t[i]=s.join("\n")),t}exportCSV(e){let t=[],n=[];switch(e){case"products":t=this.getProducts(),n=Ai;break;case"users":t=this.getUsers(),n=Ni;break;case"sales":t=this.getSales(),n=Li;break;case"debts":t=this.getDebts(),n=Oi;break;case"expenses":t=this.getExpenses(),n=Vi;break;case"employee_payments":t=this.getEmployeePayments(),n=qi}return Ii.arrayToCSV(t,n)}importCSV(e,t,n=!1){try{let i=[],s=[];switch(e){case"products":i=Ai,s=n?[]:this.getProducts();break;case"users":i=Ni,s=n?[]:this.getUsers();break;default:return{success:!1,message:"Type de données non supporté",errors:[]}}const r=Ii.csvToArray(t,i),a=Ii.validateCSVData(r,i);if(!a.isValid)return{success:!1,message:"Données invalides",errors:a.errors};let o=r;if(!n&&s.length>0){const e=new Set(s.map(e=>e.id)),t=r.filter(t=>!e.has(t.id));o=[...s,...t]}switch(e){case"products":this.setProducts(o);break;case"users":this.setUsers(o)}return{success:!0,message:`${r.length} éléments importés avec succès`,errors:[]}}catch(i){return{success:!1,message:"Erreur lors de l'importation: "+i.message,errors:[i.message]}}}},$i=wi("Preferences",{web:()=>vi(()=>import("./web-C6IHjsrw.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url).then(e=>new e.PreferencesWeb)});const Wi=new class{constructor(){n(this,"CSV_PREFIX","smartboutique_csv_")}async setCSV(e,t,n){try{const i=Ii.arrayToCSV(t,n);await $i.set({key:this.CSV_PREFIX+e,value:i})}catch(i){throw console.error(`Erreur lors de la sauvegarde CSV ${e}:`,i),i}}async getCSV(e,t,n=[]){try{const i=await $i.get({key:this.CSV_PREFIX+e});return i.value?Ii.csvToArray(i.value,t):n}catch(i){return console.error(`Erreur lors de la lecture CSV ${e}:`,i),n}}async getProducts(){return(await this.getCSV("products",Ai,[])).map(e=>{!e.prixAchatCDF&&e.prixCDF&&(e.prixAchatCDF=.7*e.prixCDF),e.prixAchatCDF=Number(e.prixAchatCDF)||0,e.prixAchatUSD=e.prixAchatUSD?Number(e.prixAchatUSD):void 0,e.prixCDF=Number(e.prixCDF)||0,e.prixUSD=e.prixUSD?Number(e.prixUSD):void 0,e.beneficeUnitaireCDF=e.beneficeUnitaireCDF?Number(e.beneficeUnitaireCDF):void 0,e.beneficeUnitaireUSD=e.beneficeUnitaireUSD?Number(e.beneficeUnitaireUSD):void 0,e.stock=Number(e.stock)||0,e.stockMin=Number(e.stockMin)||0;const t=(new Date).toISOString();return e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())||(e.dateCreation=t),e.dateModification&&!isNaN(new Date(e.dateModification).getTime())||(e.dateModification=t),e})}async setProducts(e){await this.setCSV("products",e,Ai)}async getUsers(){return this.getCSV("users",Ni,[])}async setUsers(e){await this.setCSV("users",e,Ni)}async getSales(){return(await this.getCSV("sales",Li,[])).map(e=>(e.totalCDF=Number(e.totalCDF)||0,e.totalUSD=e.totalUSD?Number(e.totalUSD):void 0,e.produits=(e.produits||[]).map(e=>({...e,quantite:Number(e.quantite)||0,prixUnitaireCDF:Number(e.prixUnitaireCDF)||0,prixUnitaireUSD:e.prixUnitaireUSD?Number(e.prixUnitaireUSD):void 0,totalCDF:Number(e.totalCDF)||0,totalUSD:e.totalUSD?Number(e.totalUSD):void 0})),e))}async setSales(e){await this.setCSV("sales",e,Li)}async getDebts(){return(await this.getCSV("debts",Oi,[])).map(e=>(e.statutPaiement||(e.statutPaiement="paid"===e.statut?"paye":"impaye"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantTotalUSD=e.montantTotalUSD?Number(e.montantTotalUSD):void 0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantPayeUSD=e.montantPayeUSD?Number(e.montantPayeUSD):void 0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,e.montantRestantUSD=e.montantRestantUSD?Number(e.montantRestantUSD):void 0,e.paiements=(e.paiements||[]).map(e=>({...e,montantCDF:Number(e.montantCDF)||0,montantUSD:e.montantUSD?Number(e.montantUSD):void 0})),e))}async setDebts(e){await this.setCSV("debts",e,Oi)}async getExpenses(){return this.getCSV("expenses",Vi,[])}async setExpenses(e){await this.setCSV("expenses",e,Vi)}async getSettings(){const e=await $i.get({key:this.CSV_PREFIX+"settings"});if(e.value)try{return JSON.parse(e.value)}catch(t){console.error("Erreur lors du parsing des paramètres:",t)}return{tauxChangeUSDCDF:2800,seuilStockBas:10,categories:[{id:"1",nom:"Électronique",description:"Appareils électroniques",couleur:"#2196F3"},{id:"2",nom:"Vêtements",description:"Vêtements et accessoires",couleur:"#4CAF50"},{id:"3",nom:"Alimentation",description:"Produits alimentaires",couleur:"#FF9800"},{id:"4",nom:"Maison",description:"Articles pour la maison",couleur:"#9C27B0"},{id:"5",nom:"Beauté",description:"Produits de beauté",couleur:"#E91E63"},{id:"6",nom:"Boissons",description:"Boissons et breuvages",couleur:"#00BCD4"},{id:"7",nom:"Épicerie",description:"Produits d'épicerie",couleur:"#795548"},{id:"8",nom:"Livres",description:"Livres et éducation",couleur:"#607D8B"},{id:"9",nom:"Sport",description:"Articles de sport",couleur:"#FF5722"},{id:"10",nom:"Santé",description:"Produits de santé",couleur:"#8BC34A"}],entreprise:{nom:"SmartBoutique",adresse:"Kinshasa, RDC",telephone:"+243 000 000 000",email:"<EMAIL>",rccm:"",idNat:"",logo:""}}}async setSettings(e){await $i.set({key:this.CSV_PREFIX+"settings",value:JSON.stringify(e)})}async getCurrentUser(){const e=await $i.get({key:this.CSV_PREFIX+"currentUser"});return e.value?JSON.parse(e.value):null}async setCurrentUser(e){await $i.set({key:this.CSV_PREFIX+"currentUser",value:JSON.stringify(e)})}async initializeDefaultData(){if(0===(await this.getUsers()).length){const e=[{id:"1",nom:"Super Admin",email:"<EMAIL>",role:"super_admin",motDePasse:"admin123",dateCreation:(new Date).toISOString(),actif:!0},{id:"2",nom:"Gestionnaire",email:"<EMAIL>",role:"admin",motDePasse:"manager123",dateCreation:(new Date).toISOString(),actif:!0},{id:"3",nom:"Employé",email:"<EMAIL>",role:"employee",motDePasse:"employee123",dateCreation:(new Date).toISOString(),actif:!0}];await this.setUsers(e)}0===(await this.getProducts()).length&&await this.initializeProductCatalog();0===(await this.getDebts()).length&&await this.initializeSampleDebts()}async initializeProductCatalog(){const e=[{id:"1",nom:"iPhone 15",description:"Smartphone Apple iPhone 15 128GB",prixCDF:224e4,prixUSD:800,codeQR:"SB12345678ABCD",categorie:"Électronique",stock:25,stockMin:5,codeBarres:"1234567890123",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"2",nom:"T-shirt Nike",description:"T-shirt Nike en coton, taille M",prixCDF:98e3,prixUSD:35,codeQR:"SB12345679EFGH",categorie:"Vêtements",stock:50,stockMin:10,codeBarres:"1234567890124",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"3",nom:"Café Arabica",description:"Café Arabica premium 500g",prixCDF:33600,prixUSD:12,codeQR:"SB12345680IJKL",categorie:"Alimentation",stock:8,stockMin:15,codeBarres:"1234567890125",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"4",nom:"Sucre",description:"Sucre blanc cristallisé 1kg",prixCDF:8400,prixUSD:3,codeQR:"SB12345681MNOP",categorie:"Épicerie",stock:120,stockMin:20,codeBarres:"1234567890126",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"5",nom:"Riz",description:"Riz blanc parfumé 5kg",prixCDF:42e3,prixUSD:15,codeQR:"SB12345682QRST",categorie:"Alimentation",stock:80,stockMin:15,codeBarres:"1234567890127",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"6",nom:"Sel",description:"Sel de cuisine iodé 500g",prixCDF:2800,prixUSD:1,codeQR:"SB12345683UVWX",categorie:"Épicerie",stock:200,stockMin:30,codeBarres:"1234567890128",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"7",nom:"Lait",description:"Lait entier UHT 1 litre",prixCDF:5600,prixUSD:2,codeQR:"SB12345684YZAB",categorie:"Boissons",stock:60,stockMin:12,codeBarres:"1234567890129",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"8",nom:"Thé",description:"Thé noir en sachets, boîte de 25",prixCDF:11200,prixUSD:4,codeQR:"SB12345685CDEF",categorie:"Boissons",stock:45,stockMin:10,codeBarres:"1234567890130",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"9",nom:"Vestes",description:"Veste en jean unisexe, taille L",prixCDF:14e4,prixUSD:50,codeQR:"SB12345686GHIJ",categorie:"Vêtements",stock:30,stockMin:5,codeBarres:"1234567890131",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"10",nom:"Livres",description:'Roman français "Le Petit Prince"',prixCDF:22400,prixUSD:8,codeQR:"SB12345687KLMN",categorie:"Livres",stock:25,stockMin:5,codeBarres:"1234567890132",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"11",nom:"Huile de Palme",description:"Huile de palme rouge 1 litre",prixCDF:16800,prixUSD:6,codeQR:"SB12345688OPQR",categorie:"Alimentation",stock:40,stockMin:8,codeBarres:"1234567890133",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()},{id:"12",nom:"Farine de Maïs",description:"Farine de maïs blanche 2kg",prixCDF:11200,prixUSD:4,codeQR:"SB12345689STUV",categorie:"Alimentation",stock:75,stockMin:15,codeBarres:"1234567890134",dateCreation:(new Date).toISOString(),dateModification:(new Date).toISOString()}];await this.setProducts(e)}async initializeSampleDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()-12096e5),i=new Date(e.getTime()+2592e6),s=new Date(e.getTime()+12096e5),r=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:i.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 998 765 432",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:25e4,montantTotalUSD:89.29,montantPayeCDF:1e5,montantPayeUSD:35.71,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:n.toISOString(),dateEcheance:s.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:1e5,montantUSD:35.71,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e5).toISOString(),notes:"Paiement partiel en espèces"}],notes:"Paiement partiel effectué - Vêtements et accessoires"},{id:"DET-003",venteId:"VTE-CREDIT-003",nomClient:"Joseph Kabila Kabange",telephoneClient:"+243 811 222 333",adresseClient:"Avenue de la Paix, Lubumbashi",montantTotalCDF:18e4,montantTotalUSD:64.29,montantPayeCDF:18e4,montantPayeUSD:64.29,montantRestantCDF:0,montantRestantUSD:0,dateCreation:n.toISOString(),dateEcheance:i.toISOString(),statut:"paid",statutPaiement:"paye",paiements:[{id:"PAY-002",montantCDF:18e4,montantUSD:64.29,methodePaiement:"mobile_money",datePaiement:new Date(e.getTime()-1728e5).toISOString(),notes:"Paiement complet via Mobile Money"}],notes:"Dette entièrement payée - Alimentation et boissons"},{id:"DET-004",venteId:"VTE-CREDIT-004",nomClient:"Fatou Diallo Sankara",telephoneClient:"+243 977 888 999",adresseClient:"Quartier Matonge, Kinshasa",montantTotalCDF:32e4,montantTotalUSD:114.29,montantPayeCDF:8e4,montantPayeUSD:28.57,montantRestantCDF:24e4,montantRestantUSD:85.71,dateCreation:new Date(e.getTime()-3888e6).toISOString(),dateEcheance:new Date(e.getTime()-1296e6).toISOString(),statut:"overdue",statutPaiement:"impaye",paiements:[{id:"PAY-003",montantCDF:8e4,montantUSD:28.57,methodePaiement:"cash",datePaiement:new Date(e.getTime()-2592e6).toISOString(),notes:"Paiement partiel initial"}],notes:"Dette en retard - Nécessite suivi"}];await this.setDebts(r)}};let Xi=null,Qi=null,Ji=null;const Hi="undefined"==typeof window&&"undefined"!=typeof process&&(null==(e=process.versions)?void 0:e.electron);if(Hi)try{Xi=require("better-sqlite3"),Qi=require("electron").app,Ji=require("path").join}catch(er){console.warn("better-sqlite3 not available:",er)}const Yi=new class{constructor(){if(n(this,"db"),n(this,"dbPath"),n(this,"isAvailable",!1),Hi&&Xi)try{const e=(null==Qi?void 0:Qi.getPath("userData"))||"./data";this.dbPath=Ji(e,"smartboutique.db"),this.initializeDatabase(),this.isAvailable=!0}catch(er){console.error("Failed to initialize SQLite:",er)}else console.warn("SQLite not available in this context")}initializeDatabase(){if(!Xi)throw new Error("better-sqlite3 not available");this.db=new Xi(this.dbPath),this.db.pragma("journal_mode = WAL"),this.db.pragma("synchronous = NORMAL"),this.db.pragma("cache_size = 1000"),this.db.pragma("temp_store = memory"),this.createTables(),this.createIndexes()}checkAvailability(){if(!this.isAvailable)throw new Error("SQLite storage not available in this context")}createTables(){this.db.exec("\n      CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      )\n    "),this.db.exec("\n      CREATE TABLE IF NOT EXISTS employee_payments (\n        id TEXT PRIMARY KEY,\n        nomEmploye TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        datePaiement TEXT NOT NULL,\n        methodePaiement TEXT NOT NULL,\n        notes TEXT,\n        creePar TEXT NOT NULL,\n        dateCreation TEXT NOT NULL,\n        dateModification TEXT\n      )\n    ")}createIndexes(){this.db.exec("\n      CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);\n      CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);\n      CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);\n      CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);\n      CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);\n      CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);\n      CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);\n      CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_date ON employee_payments(datePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_employe ON employee_payments(nomEmploye);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_methode ON employee_payments(methodePaiement);\n      CREATE INDEX IF NOT EXISTS idx_employee_payments_cree_par ON employee_payments(creePar);\n    ")}getProducts(){this.checkAvailability();return this.db.prepare("SELECT * FROM products ORDER BY nom").all()}getProduct(e){this.checkAvailability();return this.db.prepare("SELECT * FROM products WHERE id = ?").get(e)}setProducts(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM products").run();const t=this.db.prepare("\n        INSERT INTO products (\n          id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n          beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n          stockMin, codeBarres, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.description,n.prixAchatCDF,n.prixAchatUSD,n.prixCDF,n.prixUSD,n.beneficeUnitaireCDF,n.beneficeUnitaireUSD,n.codeQR,n.categorie,n.stock,n.stockMin,n.codeBarres,n.dateCreation,n.dateModification)})(e)}addProduct(e){this.db.prepare("\n      INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification)}updateProduct(e){this.db.prepare("\n      UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?\n    ").run(e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id)}deleteProduct(e){this.db.prepare("DELETE FROM products WHERE id = ?").run(e)}getUsers(){return this.db.prepare("SELECT * FROM users ORDER BY nom").all().map(e=>({...e,actif:Boolean(e.actif)}))}setUsers(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM users").run();const t=this.db.prepare("\n        INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif)\n        VALUES (?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nom,n.email,n.role,n.motDePasse,n.dateCreation,n.actif?1:0)})(e)}getSales(){return this.db.prepare("SELECT * FROM sales ORDER BY date DESC").all().map(e=>({...e,produits:JSON.parse(e.produits)}))}setSales(e){this.db.transaction(e=>{this.db.prepare("DELETE FROM sales").run();const t=this.db.prepare("\n        INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu)\n        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.datevente,n.nomClient,JSON.stringify(n.produits),n.totalCDF,n.totalUSD,n.methodePaiement,n.typeVente,n.vendeur,n.numeroRecu)})(e)}getEmployeePayments(){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments ORDER BY datePaiement DESC").all()}getEmployeePayment(e){this.checkAvailability();return this.db.prepare("SELECT * FROM employee_payments WHERE id = ?").get(e)}addEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      INSERT INTO employee_payments (\n        id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n        notes, creePar, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n    ").run(e.id,e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.creePar,e.dateCreation,e.dateModification)}updateEmployeePayment(e){this.checkAvailability();this.db.prepare("\n      UPDATE employee_payments SET\n        nomEmploye = ?, montantCDF = ?, montantUSD = ?, datePaiement = ?,\n        methodePaiement = ?, notes = ?, dateModification = ?\n      WHERE id = ?\n    ").run(e.nomEmploye,e.montantCDF,e.montantUSD,e.datePaiement,e.methodePaiement,e.notes,e.dateModification,e.id)}deleteEmployeePayment(e){this.checkAvailability();this.db.prepare("DELETE FROM employee_payments WHERE id = ?").run(e)}setEmployeePayments(e){this.checkAvailability();this.db.transaction(e=>{this.db.prepare("DELETE FROM employee_payments").run();const t=this.db.prepare("\n        INSERT INTO employee_payments (\n          id, nomEmploye, montantCDF, montantUSD, datePaiement, methodePaiement,\n          notes, creePar, dateCreation, dateModification\n        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)\n      ");for(const n of e)t.run(n.id,n.nomEmploye,n.montantCDF,n.montantUSD,n.datePaiement,n.methodePaiement,n.notes,n.creePar,n.dateCreation,n.dateModification)})(e)}migrateFromCSV(e){console.log("Starting migration from CSV to SQLite...");this.db.transaction(()=>{var t,n,i;(null==(t=e.products)?void 0:t.length)&&this.setProducts(e.products),(null==(n=e.users)?void 0:n.length)&&this.setUsers(e.users),(null==(i=e.sales)?void 0:i.length)&&this.setSales(e.sales)})(),console.log("Migration completed successfully")}exportToCSV(){return{products:"",users:"",sales:""}}close(){this.db.close()}getStats(){const e=this.db.prepare("SELECT COUNT(*) as count FROM products").get(),t=this.db.prepare("SELECT COUNT(*) as count FROM users").get(),n=this.db.prepare("SELECT COUNT(*) as count FROM sales").get(),i=this.db.prepare("SELECT COUNT(*) as count FROM employee_payments").get();return{products:e.count,users:t.count,sales:n.count,employeePayments:i.count,dbSize:0}}};class Ki{constructor(e){this.sqlite=e,this._connectionDict=new Map}async initWebStore(){try{return await this.sqlite.initWebStore(),Promise.resolve()}catch(e){return Promise.reject(e)}}async saveToStore(e){try{return await this.sqlite.saveToStore({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async saveToLocalDisk(e){try{return await this.sqlite.saveToLocalDisk({database:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getFromLocalDiskToStore(e){const t=null==e||e;try{return await this.sqlite.getFromLocalDiskToStore({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async echo(e){try{const t=await this.sqlite.echo({value:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isSecretStored(){try{const e=await this.sqlite.isSecretStored();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async setEncryptionSecret(e){try{return await this.sqlite.setEncryptionSecret({passphrase:e}),Promise.resolve()}catch(t){return Promise.reject(t)}}async changeEncryptionSecret(e,t){try{return await this.sqlite.changeEncryptionSecret({passphrase:e,oldpassphrase:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async clearEncryptionSecret(){try{return await this.sqlite.clearEncryptionSecret(),Promise.resolve()}catch(e){return Promise.reject(e)}}async checkEncryptionSecret(e){try{const t=await this.sqlite.checkEncryptionSecret({passphrase:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async addUpgradeStatement(e,t){try{return e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.addUpgradeStatement({database:e,upgrade:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async createConnection(e,t,n,i,s){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.createConnection({database:e,encrypted:t,mode:n,version:i,readonly:s});const r=new Gi(e,s,this.sqlite),a=s?`RO_${e}`:`RW_${e}`;return this._connectionDict.set(a,r),Promise.resolve(r)}catch(r){return Promise.reject(r)}}async closeConnection(e,t){try{e.endsWith(".db")&&(e=e.slice(0,-3)),await this.sqlite.closeConnection({database:e,readonly:t});const n=t?`RO_${e}`:`RW_${e}`;return this._connectionDict.delete(n),Promise.resolve()}catch(n){return Promise.reject(n)}}async isConnection(e,t){const n={};e.endsWith(".db")&&(e=e.slice(0,-3));const i=t?`RO_${e}`:`RW_${e}`;return n.result=this._connectionDict.has(i),Promise.resolve(n)}async retrieveConnection(e,t){e.endsWith(".db")&&(e=e.slice(0,-3));const n=t?`RO_${e}`:`RW_${e}`;if(this._connectionDict.has(n)){const t=this._connectionDict.get(n);return void 0!==t?Promise.resolve(t):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async getNCDatabasePath(e,t){try{const n=await this.sqlite.getNCDatabasePath({path:e,database:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async createNCConnection(e,t){try{await this.sqlite.createNCConnection({databasePath:e,version:t});const n=new Gi(e,!0,this.sqlite),i=`RO_${e})`;return this._connectionDict.set(i,n),Promise.resolve(n)}catch(n){return Promise.reject(n)}}async closeNCConnection(e){try{await this.sqlite.closeNCConnection({databasePath:e});const t=`RO_${e})`;return this._connectionDict.delete(t),Promise.resolve()}catch(t){return Promise.reject(t)}}async isNCConnection(e){const t={},n=`RO_${e})`;return t.result=this._connectionDict.has(n),Promise.resolve(t)}async retrieveNCConnection(e){if(this._connectionDict.has(e)){const t=`RO_${e})`,n=this._connectionDict.get(t);return void 0!==n?Promise.resolve(n):Promise.reject(`Connection ${e} is undefined`)}return Promise.reject(`Connection ${e} does not exist`)}async isNCDatabase(e){try{const t=await this.sqlite.isNCDatabase({databasePath:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async retrieveAllConnections(){return this._connectionDict}async closeAllConnections(){const e=new Map;try{for(const t of this._connectionDict.keys()){const n=t.substring(3),i="RO_"===t.substring(0,3);await this.sqlite.closeConnection({database:n,readonly:i}),e.set(t,null)}for(const t of e.keys())this._connectionDict.delete(t);return Promise.resolve()}catch(t){return Promise.reject(t)}}async checkConnectionsConsistency(){try{const e=[...this._connectionDict.keys()],t=[],n=[];for(const s of e)t.push(s.substring(0,2)),n.push(s.substring(3));const i=await this.sqlite.checkConnectionsConsistency({dbNames:n,openModes:t});return i.result||(this._connectionDict=new Map),Promise.resolve(i)}catch(e){return this._connectionDict=new Map,Promise.reject(e)}}async importFromJson(e){try{const t=await this.sqlite.importFromJson({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isJsonValid(e){try{const t=await this.sqlite.isJsonValid({jsonstring:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async copyFromAssets(e){const t=null==e||e;try{return await this.sqlite.copyFromAssets({overwrite:t}),Promise.resolve()}catch(n){return Promise.reject(n)}}async getFromHTTPRequest(e,t){const n=null==t||t;try{return await this.sqlite.getFromHTTPRequest({url:e,overwrite:n}),Promise.resolve()}catch(i){return Promise.reject(i)}}async isDatabaseEncrypted(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabaseEncrypted({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isInConfigEncryption(){try{const e=await this.sqlite.isInConfigEncryption();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isInConfigBiometricAuth(){try{const e=await this.sqlite.isInConfigBiometricAuth();return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isDatabase(e){e.endsWith(".db")&&(e=e.slice(0,-3));try{const t=await this.sqlite.isDatabase({database:e});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async getDatabaseList(){try{const e=(await this.sqlite.getDatabaseList()).values;e.sort();const t={values:e};return Promise.resolve(t)}catch(e){return Promise.reject(e)}}async getMigratableDbList(e){const t=e||"default";try{const e=await this.sqlite.getMigratableDbList({folderPath:t});return Promise.resolve(e)}catch(n){return Promise.reject(n)}}async addSQLiteSuffix(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.addSQLiteSuffix({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(s){return Promise.reject(s)}}async deleteOldDatabases(e,t){const n=e||"default",i=t||[];try{const e=await this.sqlite.deleteOldDatabases({folderPath:n,dbNameList:i});return Promise.resolve(e)}catch(s){return Promise.reject(s)}}async moveDatabasesAndAddSuffix(e,t){const n=e||"default",i=t||[];return this.sqlite.moveDatabasesAndAddSuffix({folderPath:n,dbNameList:i})}}class Gi{constructor(e,t,n){this.dbName=e,this.readonly=t,this.sqlite=n}getConnectionDBName(){return this.dbName}getConnectionReadOnly(){return this.readonly}async open(){try{return await this.sqlite.open({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async close(){try{return await this.sqlite.close({database:this.dbName,readonly:this.readonly}),Promise.resolve()}catch(e){return Promise.reject(e)}}async beginTransaction(){try{const e=await this.sqlite.beginTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async commitTransaction(){try{const e=await this.sqlite.commitTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async rollbackTransaction(){try{const e=await this.sqlite.rollbackTransaction({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTransactionActive(){try{const e=await this.sqlite.isTransactionActive({database:this.dbName});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async loadExtension(e){try{return await this.sqlite.loadExtension({database:this.dbName,path:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async enableLoadExtension(e){try{return await this.sqlite.enableLoadExtension({database:this.dbName,toggle:e,readonly:this.readonly}),Promise.resolve()}catch(t){return Promise.reject(t)}}async getUrl(){try{const e=await this.sqlite.getUrl({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getVersion(){try{const e=await this.sqlite.getVersion({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async getTableList(){try{const e=await this.sqlite.getTableList({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async execute(e,t=!0,n=!0){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const i=await this.sqlite.execute({database:this.dbName,statements:e,transaction:t,readonly:!1,isSQL92:n});return Promise.resolve(i)}}catch(i){return Promise.reject(i)}}async query(e,t,n=!0){let i;try{return i=t&&t.length>0?await this.sqlite.query({database:this.dbName,statement:e,values:t,readonly:this.readonly,isSQL92:!0}):await this.sqlite.query({database:this.dbName,statement:e,values:[],readonly:this.readonly,isSQL92:n}),i=await this.reorderRows(i),Promise.resolve(i)}catch(s){return Promise.reject(s)}}async run(e,t,n=!0,i="no",s=!0){let r;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(r=t&&t.length>0?await this.sqlite.run({database:this.dbName,statement:e,values:t,transaction:n,readonly:!1,returnMode:i,isSQL92:!0}):await this.sqlite.run({database:this.dbName,statement:e,values:[],transaction:n,readonly:!1,returnMode:i,isSQL92:s}),r.changes=await this.reorderRows(r.changes),Promise.resolve(r))}catch(a){return Promise.reject(a)}}async executeSet(e,t=!0,n="no",i=!0){let s;try{return this.readonly?Promise.reject("not allowed in read-only mode"):(s=await this.sqlite.executeSet({database:this.dbName,set:e,transaction:t,readonly:!1,returnMode:n,isSQL92:i}),s.changes=await this.reorderRows(s.changes),Promise.resolve(s))}catch(r){return Promise.reject(r)}}async isExists(){try{const e=await this.sqlite.isDBExists({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async isTable(e){try{const t=await this.sqlite.isTableExists({database:this.dbName,table:e,readonly:this.readonly});return Promise.resolve(t)}catch(t){return Promise.reject(t)}}async isDBOpen(){try{const e=await this.sqlite.isDBOpen({database:this.dbName,readonly:this.readonly});return Promise.resolve(e)}catch(e){return Promise.reject(e)}}async delete(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteDatabase({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async createSyncTable(){try{if(this.readonly)return Promise.reject("not allowed in read-only mode");{const e=await this.sqlite.createSyncTable({database:this.dbName,readonly:!1});return Promise.resolve(e)}}catch(e){return Promise.reject(e)}}async setSyncDate(e){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.setSyncDate({database:this.dbName,syncdate:e,readonly:!1}),Promise.resolve())}catch(t){return Promise.reject(t)}}async getSyncDate(){try{const e=await this.sqlite.getSyncDate({database:this.dbName,readonly:this.readonly});let t="";return e.syncDate>0&&(t=new Date(1e3*e.syncDate).toISOString()),Promise.resolve(t)}catch(e){return Promise.reject(e)}}async exportToJson(e,t=!1){try{const n=await this.sqlite.exportToJson({database:this.dbName,jsonexportmode:e,readonly:this.readonly,encrypted:t});return Promise.resolve(n)}catch(n){return Promise.reject(n)}}async deleteExportedRows(){try{return this.readonly?Promise.reject("not allowed in read-only mode"):(await this.sqlite.deleteExportedRows({database:this.dbName,readonly:!1}),Promise.resolve())}catch(e){return Promise.reject(e)}}async executeTransaction(e,t=!0){let n=0,i=!1;if(this.readonly)return Promise.reject("not allowed in read-only mode");if(await this.sqlite.beginTransaction({database:this.dbName}),i=await this.sqlite.isTransactionActive({database:this.dbName}),!i)return Promise.reject("After Begin Transaction, no transaction active");try{for(const s of e){if("object"!=typeof s||!("statement"in s))throw new Error("Error a task.statement must be provided");if("values"in s&&s.values&&s.values.length>0){const e=s.statement.toUpperCase().includes("RETURNING")?"all":"no",i=await this.sqlite.run({database:this.dbName,statement:s.statement,values:s.values,transaction:!1,readonly:!1,returnMode:e,isSQL92:t});if(i.changes.changes<0)throw new Error("Error in transaction method run ");n+=i.changes.changes}else{const e=await this.sqlite.execute({database:this.dbName,statements:s.statement,transaction:!1,readonly:!1});if(e.changes.changes<0)throw new Error("Error in transaction method execute ");n+=e.changes.changes}}n+=(await this.sqlite.commitTransaction({database:this.dbName})).changes.changes;const i={changes:{changes:n}};return Promise.resolve(i)}catch(s){const e=s.message?s.message:s;return await this.sqlite.rollbackTransaction({database:this.dbName}),Promise.reject(e)}}async reorderRows(e){const t=e;if((null==e?void 0:e.values)&&"object"==typeof e.values[0]&&Object.keys(e.values[0]).includes("ios_columns")){const n=e.values[0].ios_columns,i=[];for(let t=1;t<e.values.length;t++){const s=e.values[t],r={};for(const e of n)r[e]=s[e];i.push(r)}t.values=i}return Promise.resolve(t)}}const Zi=wi("CapacitorSQLite",{web:()=>vi(()=>import("./web-Bih2Ec3i.js"),__vite__mapDeps([5,1,2,3,4]),import.meta.url).then(e=>new e.CapacitorSQLiteWeb),electron:()=>window.CapacitorCustomPlatform.plugins.CapacitorSQLite});const es=new class{constructor(){n(this,"sqlite"),n(this,"db",null),n(this,"DB_NAME","smartboutique.db"),n(this,"DB_VERSION",1),n(this,"isInitialized",!1),this.sqlite=new Ki(Zi)}async initialize(){if(!this.isInitialized)try{if(console.log("Initializing mobile SQLite database..."),!Ci.isNativePlatform())throw new Error("SQLite is only supported on native platforms (Android/iOS)");const e=await this.sqlite.checkConnectionsConsistency(),t=(await this.sqlite.isConnection(this.DB_NAME,!1)).result;e.result&&t?this.db=await this.sqlite.retrieveConnection(this.DB_NAME,!1):this.db=await this.sqlite.createConnection(this.DB_NAME,!1,"no-encryption",this.DB_VERSION,!1),await this.db.open(),await this.createTables(),await this.createIndexes(),this.isInitialized=!0,console.log("Mobile SQLite database initialized successfully")}catch(er){throw console.error("Failed to initialize mobile SQLite database:",er),er}}async createTables(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE TABLE IF NOT EXISTS products (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        description TEXT,\n        prixAchatCDF REAL NOT NULL,\n        prixAchatUSD REAL,\n        prixCDF REAL NOT NULL,\n        prixUSD REAL,\n        beneficeUnitaireCDF REAL,\n        beneficeUnitaireUSD REAL,\n        codeQR TEXT,\n        categorie TEXT,\n        stock INTEGER,\n        stockMin INTEGER,\n        codeBarres TEXT,\n        dateCreation TEXT,\n        dateModification TEXT\n      );","CREATE TABLE IF NOT EXISTS users (\n        id TEXT PRIMARY KEY,\n        nom TEXT NOT NULL,\n        email TEXT NOT NULL UNIQUE,\n        role TEXT NOT NULL,\n        motDePasse TEXT NOT NULL,\n        dateCreation TEXT,\n        actif INTEGER DEFAULT 1\n      );","CREATE TABLE IF NOT EXISTS sales (\n        id TEXT PRIMARY KEY,\n        date TEXT NOT NULL,\n        client TEXT,\n        produits TEXT NOT NULL,\n        totalCDF REAL NOT NULL,\n        totalUSD REAL,\n        typePaiement TEXT NOT NULL,\n        typeVente TEXT NOT NULL,\n        vendeur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS debts (\n        id TEXT PRIMARY KEY,\n        client TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        dateCreation TEXT NOT NULL,\n        dateEcheance TEXT,\n        statut TEXT NOT NULL,\n        description TEXT,\n        vendeur TEXT NOT NULL\n      );","CREATE TABLE IF NOT EXISTS expenses (\n        id TEXT PRIMARY KEY,\n        description TEXT NOT NULL,\n        montantCDF REAL NOT NULL,\n        montantUSD REAL,\n        date TEXT NOT NULL,\n        categorie TEXT NOT NULL,\n        utilisateur TEXT NOT NULL,\n        numeroRecu TEXT\n      );","CREATE TABLE IF NOT EXISTS settings (\n        key TEXT PRIMARY KEY,\n        value TEXT NOT NULL\n      );"];for(const t of e)await this.db.execute(t)}async createIndexes(){if(!this.db)throw new Error("Database not initialized");const e=["CREATE INDEX IF NOT EXISTS idx_products_categorie ON products(categorie);","CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock);","CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(date);","CREATE INDEX IF NOT EXISTS idx_sales_vendeur ON sales(vendeur);","CREATE INDEX IF NOT EXISTS idx_debts_statut ON debts(statut);","CREATE INDEX IF NOT EXISTS idx_debts_client ON debts(client);","CREATE INDEX IF NOT EXISTS idx_expenses_date ON expenses(date);","CREATE INDEX IF NOT EXISTS idx_expenses_categorie ON expenses(categorie);"];for(const t of e)await this.db.execute(t)}async ensureInitialized(){this.isInitialized||await this.initialize()}async getProducts(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM products ORDER BY nom")).values}async getProduct(e){var t;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return null==(t=(await this.db.query("SELECT * FROM products WHERE id = ?",[e])).values)?void 0:t[0]}async setProducts(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM products");for(const t of e)await this.db.run("INSERT INTO products (\n            id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n            beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n            stockMin, codeBarres, dateCreation, dateModification\n          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.description,t.prixAchatCDF,t.prixAchatUSD,t.prixCDF,t.prixUSD,t.beneficeUnitaireCDF,t.beneficeUnitaireUSD,t.codeQR,t.categorie,t.stock,t.stockMin,t.codeBarres,t.dateCreation,t.dateModification]);await this.db.commitTransaction()}catch(er){throw await this.db.rollbackTransaction(),er}}async addProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("INSERT INTO products (\n        id, nom, description, prixAchatCDF, prixAchatUSD, prixCDF, prixUSD,\n        beneficeUnitaireCDF, beneficeUnitaireUSD, codeQR, categorie, stock,\n        stockMin, codeBarres, dateCreation, dateModification\n      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[e.id,e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateCreation,e.dateModification])}async updateProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("UPDATE products SET\n        nom = ?, description = ?, prixAchatCDF = ?, prixAchatUSD = ?,\n        prixCDF = ?, prixUSD = ?, beneficeUnitaireCDF = ?, beneficeUnitaireUSD = ?,\n        codeQR = ?, categorie = ?, stock = ?, stockMin = ?, codeBarres = ?,\n        dateModification = ?\n      WHERE id = ?",[e.nom,e.description,e.prixAchatCDF,e.prixAchatUSD,e.prixCDF,e.prixUSD,e.beneficeUnitaireCDF,e.beneficeUnitaireUSD,e.codeQR,e.categorie,e.stock,e.stockMin,e.codeBarres,e.dateModification,e.id])}async deleteProduct(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.run("DELETE FROM products WHERE id = ?",[e])}async getUsers(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM users ORDER BY nom")).values.map(e=>({...e,actif:Boolean(e.actif)}))}async setUsers(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM users");for(const t of e)await this.db.run("INSERT INTO users (id, nom, email, role, motDePasse, dateCreation, actif) VALUES (?, ?, ?, ?, ?, ?, ?)",[t.id,t.nom,t.email,t.role,t.motDePasse,t.dateCreation,t.actif?1:0]);await this.db.commitTransaction()}catch(er){throw await this.db.rollbackTransaction(),er}}async getSales(){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");return(await this.db.query("SELECT * FROM sales ORDER BY date DESC")).values.map(e=>({...e,produits:JSON.parse(e.produits),datevente:e.date,nomClient:e.client,methodePaiement:e.typePaiement}))}async setSales(e){if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");await this.db.beginTransaction();try{await this.db.run("DELETE FROM sales");for(const t of e)await this.db.run("INSERT INTO sales (id, date, client, produits, totalCDF, totalUSD, typePaiement, typeVente, vendeur, numeroRecu) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)",[t.id,t.datevente,t.nomClient,JSON.stringify(t.produits),t.totalCDF,t.totalUSD,t.methodePaiement,t.typeVente,t.vendeur,t.numeroRecu]);await this.db.commitTransaction()}catch(er){throw await this.db.rollbackTransaction(),er}}async close(){this.db&&(await this.db.close(),await this.sqlite.closeConnection(this.DB_NAME,!1),this.db=null,this.isInitialized=!1)}async getStats(){var e,t,n,i,s,r;if(await this.ensureInitialized(),!this.db)throw new Error("Database not initialized");const[a,o,l]=await Promise.all([this.db.query("SELECT COUNT(*) as count FROM products"),this.db.query("SELECT COUNT(*) as count FROM users"),this.db.query("SELECT COUNT(*) as count FROM sales")]);return{products:(null==(t=null==(e=a.values)?void 0:e[0])?void 0:t.count)||0,users:(null==(i=null==(n=o.values)?void 0:n[0])?void 0:i.count)||0,sales:(null==(r=null==(s=l.values)?void 0:s[0])?void 0:r.count)||0,dbSize:0}}};const ts=new class{constructor(){n(this,"MIGRATION_FLAG_KEY","smartboutique_mobile_migrated_to_sqlite")}async isMigrationCompleted(){return"true"===(await $i.get({key:this.MIGRATION_FLAG_KEY})).value}async markMigrationCompleted(){await $i.set({key:this.MIGRATION_FLAG_KEY,value:"true"})}async migrateToSQLite(){const e={success:!1,message:"",migratedCounts:{products:0,users:0,sales:0,debts:0,expenses:0},errors:[]};try{if(console.log("Starting mobile migration from CSV to SQLite..."),await this.isMigrationCompleted())return e.success=!0,e.message="Migration mobile déjà effectuée",e;await es.initialize();const t=await this.extractCSVData();if(!this.validateCSVData(t))return e.errors.push("Données CSV mobiles invalides ou corrompues"),e.message="Échec de la validation des données CSV mobiles",e;await this.createBackup(t),await this.performMigration(t,e),await this.verifyMigration(t,e)?(await this.markMigrationCompleted(),e.success=!0,e.message=`Migration mobile réussie: ${e.migratedCounts.products} produits, ${e.migratedCounts.users} utilisateurs, ${e.migratedCounts.sales} ventes migrées`):e.message="Échec de la vérification de la migration mobile"}catch(er){console.error("Mobile migration error:",er),e.errors.push(`Erreur de migration mobile: ${er.message}`),e.message="Échec de la migration mobile"}return e}async extractCSVData(){return console.log("Extracting data from CSV Capacitor Preferences..."),{products:await Wi.getProducts()||[],users:await Wi.getUsers()||[],sales:await Wi.getSales()||[],debts:await Wi.getDebts()||[],expenses:await Wi.getExpenses()||[]}}validateCSVData(e){try{if(!e||"object"!=typeof e)return!1;const{products:t,users:n,sales:i,debts:s,expenses:r}=e;if(t&&Array.isArray(t))for(const e of t)if(!e.id||!e.nom||"number"!=typeof e.prixCDF)return console.warn("Invalid mobile product found:",e),!1;if(n&&Array.isArray(n))for(const e of n)if(!(e.id&&e.nom&&e.email&&e.role))return console.warn("Invalid mobile user found:",e),!1;if(i&&Array.isArray(i))for(const e of i)if(!e.id||!e.datevente||!Array.isArray(e.produits))return console.warn("Invalid mobile sale found:",e),!1;return!0}catch(er){return console.error("Mobile data validation error:",er),!1}}async createBackup(e){try{const t={timestamp:(new Date).toISOString(),platform:"mobile",data:e};await $i.set({key:"smartboutique_mobile_csv_backup",value:JSON.stringify(t)}),console.log("Mobile backup created successfully")}catch(er){throw console.error("Mobile backup creation failed:",er),new Error("Impossible de créer une sauvegarde mobile")}}async performMigration(e,t){try{const{products:n,users:i,sales:s,debts:r,expenses:a}=e;n&&n.length>0&&(await es.setProducts(n),t.migratedCounts.products=n.length,console.log(`Migrated ${n.length} mobile products`)),i&&i.length>0&&(await es.setUsers(i),t.migratedCounts.users=i.length,console.log(`Migrated ${i.length} mobile users`)),s&&s.length>0&&(await es.setSales(s),t.migratedCounts.sales=s.length,console.log(`Migrated ${s.length} mobile sales`)),console.log("Mobile migration to SQLite completed")}catch(er){throw console.error("Mobile migration execution failed:",er),new Error(`Échec de la migration mobile: ${er.message}`)}}async verifyMigration(e,t){try{const n=await es.getStats();return e.products&&e.products.length!==n.products?(t.errors.push(`Nombre de produits mobile incorrect: attendu ${e.products.length}, trouvé ${n.products}`),!1):e.users&&e.users.length!==n.users?(t.errors.push(`Nombre d'utilisateurs mobile incorrect: attendu ${e.users.length}, trouvé ${n.users}`),!1):e.sales&&e.sales.length!==n.sales?(t.errors.push(`Nombre de ventes mobile incorrect: attendu ${e.sales.length}, trouvé ${n.sales}`),!1):(console.log("Mobile migration verification successful"),!0)}catch(er){return console.error("Mobile migration verification failed:",er),t.errors.push(`Échec de la vérification mobile: ${er.message}`),!1}}async rollbackMigration(){try{console.log("Rolling back mobile migration...");const e=await $i.get({key:"smartboutique_mobile_csv_backup"});if(!e.value)return console.error("No mobile backup found for rollback"),!1;const t=JSON.parse(e.value),{data:n}=t;return n.products&&await Wi.setProducts(n.products),n.users&&await Wi.setUsers(n.users),n.sales&&await Wi.setSales(n.sales),n.debts&&await Wi.setDebts(n.debts),n.expenses&&await Wi.setExpenses(n.expenses),await $i.remove({key:this.MIGRATION_FLAG_KEY}),await es.close(),console.log("Mobile migration rollback completed"),!0}catch(er){return console.error("Mobile rollback failed:",er),!1}}async getMigrationStatus(){const e=await this.isMigrationCompleted(),t=await $i.get({key:"smartboutique_mobile_csv_backup"}),n=await $i.get({key:"smartboutique_csv_products"});return{isCompleted:e,sqliteStats:e?await es.getStats():void 0,csvDataExists:Boolean(n.value),backupExists:Boolean(t.value)}}async cleanupOldData(){if(!(await this.isMigrationCompleted()))return void console.warn("Cannot cleanup mobile data: migration not completed");const e=["smartboutique_csv_products","smartboutique_csv_users","smartboutique_csv_sales","smartboutique_csv_debts","smartboutique_csv_expenses","smartboutique_csv_settings"];for(const t of e)await $i.remove({key:t});console.log("Old mobile CSV data cleaned up")}async forceMigration(){return await $i.remove({key:this.MIGRATION_FLAG_KEY}),this.migrateToSQLite()}};const ns=new class{constructor(){n(this,"migrationChecked",!1),n(this,"mobileMigrationChecked",!1)}get storage(){return Mi()?Wi:_i}get sqliteStorage(){return Mi()?es:Yi}async checkDesktopMigration(){if(!this.migrationChecked&&!Mi()){this.migrationChecked=!0;try{return void console.log("SQLite migration temporarily disabled - using CSV storage")}catch(er){console.error("Desktop migration check failed:",er)}}}async checkMobileMigration(){if(!this.mobileMigrationChecked&&Mi()){this.mobileMigrationChecked=!0;try{if(!(await ts.isMigrationCompleted())){console.log("Starting automatic mobile migration to SQLite...");const e=await ts.migrateToSQLite();e.success?console.log("Mobile migration completed successfully:",e.message):(console.error("Mobile migration failed:",e.message,e.errors),console.log("Falling back to CSV storage on mobile"))}}catch(er){console.error("Mobile migration check failed:",er),console.log("Falling back to CSV storage on mobile due to error")}}}async checkMigration(){Mi()?await this.checkMobileMigration():await this.checkDesktopMigration()}async set(e,t){Mi()?console.warn("Generic set method not available in CSV storage"):_i.set(e,t)}async get(e,t){return Mi()?(console.warn("Generic get method not available in CSV storage"),t):_i.get(e,t)}async remove(e){Mi()?console.warn("Generic remove method not available in CSV storage"):_i.remove(e)}async clear(){Mi()?console.warn("Generic clear method not available in CSV storage"):_i.clear()}async getUsers(){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return await this.sqliteStorage.getUsers()}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}return await Wi.getUsers()}return _i.getUsers()}async setUsers(e){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return void(await this.sqliteStorage.setUsers(e))}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}await Wi.setUsers(e)}else _i.setUsers(e)}async getProducts(){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return await this.sqliteStorage.getProducts()}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}return await Wi.getProducts()}return _i.getProducts()}async setProducts(e){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return void(await this.sqliteStorage.setProducts(e))}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}await Wi.setProducts(e)}else _i.setProducts(e)}async getSales(){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return await this.sqliteStorage.getSales()}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}return await Wi.getSales()}return _i.getSales()}async setSales(e){if(await this.checkMigration(),Mi()){try{if(await ts.isMigrationCompleted())return void(await this.sqliteStorage.setSales(e))}catch(er){console.warn("Mobile SQLite failed, falling back to CSV:",er)}await Wi.setSales(e)}else _i.setSales(e)}async getDebts(){return Mi()?await Wi.getDebts():_i.getDebts()}async setDebts(e){Mi()?await Wi.setDebts(e):_i.setDebts(e)}async getDettes(){return this.getDebts()}async setDettes(e){await this.setDebts(e)}async getCreances(){return this.getDebts()}async setCreances(e){await this.setDebts(e)}async getExpenses(){return Mi()?await Wi.getExpenses():_i.getExpenses()}async setExpenses(e){Mi()?await Wi.setExpenses(e):_i.setExpenses(e)}async getEmployeePayments(){var e,t;if(Mi())return await(null==(e=Wi.getEmployeePayments)?void 0:e.call(Wi))||[];try{return Yi.getEmployeePayments()}catch(er){return console.warn("SQLite not available for employee payments, using localStorage:",er),(null==(t=_i.getEmployeePayments)?void 0:t.call(_i))||[]}}async addEmployeePayment(e){if(Mi())Wi.addEmployeePayment&&await Wi.addEmployeePayment(e);else try{Yi.addEmployeePayment(e)}catch(er){console.warn("SQLite not available for employee payments, using localStorage:",er),_i.addEmployeePayment&&_i.addEmployeePayment(e)}}async updateEmployeePayment(e){if(Mi())Wi.updateEmployeePayment&&await Wi.updateEmployeePayment(e);else try{Yi.updateEmployeePayment(e)}catch(er){console.warn("SQLite not available for employee payments, using localStorage:",er),_i.updateEmployeePayment&&_i.updateEmployeePayment(e)}}async deleteEmployeePayment(e){if(Mi())Wi.deleteEmployeePayment&&await Wi.deleteEmployeePayment(e);else try{Yi.deleteEmployeePayment(e)}catch(er){console.warn("SQLite not available for employee payments, using localStorage:",er),_i.deleteEmployeePayment&&_i.deleteEmployeePayment(e)}}async getSettings(){return Mi()?await Wi.getSettings():_i.getSettings()}async setSettings(e){Mi()?await Wi.setSettings(e):_i.setSettings(e)}async getCurrentUser(){return Mi()?await Wi.getCurrentUser():_i.getCurrentUser()}async setCurrentUser(e){Mi()?await Wi.setCurrentUser(e):_i.setCurrentUser(e)}async initializeDefaultData(){Mi()?await Wi.initializeDefaultData():_i.initializeDefaultData();0===(await this.getDebts()).length&&await this.forceInitializeDebts()}async forceInitializeDebts(){const e=new Date,t=new Date(e.getTime()-6048e5),n=new Date(e.getTime()+2592e6),i=[{id:"DET-001",venteId:"VTE-CREDIT-001",nomClient:"Jean Baptiste Mukendi",telephoneClient:"+243 812 345 678",adresseClient:"Avenue Lumumba, Kinshasa",montantTotalCDF:15e4,montantTotalUSD:53.57,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:15e4,montantRestantUSD:53.57,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[],notes:"Vente à crédit - Produits électroniques"},{id:"DET-002",venteId:"VTE-CREDIT-002",nomClient:"Marie Kabila Tshisekedi",telephoneClient:"+243 823 456 789",adresseClient:"Boulevard du 30 Juin, Kinshasa",montantTotalCDF:75e3,montantTotalUSD:26.79,montantPayeCDF:25e3,montantPayeUSD:8.93,montantRestantCDF:5e4,montantRestantUSD:17.86,dateCreation:t.toISOString(),dateEcheance:n.toISOString(),statut:"active",statutPaiement:"impaye",paiements:[{id:"PAY-001",montantCDF:25e3,montantUSD:8.93,methodePaiement:"cash",datePaiement:e.toISOString(),notes:"Paiement partiel"}],notes:"Dette partiellement payée"}];await this.setDebts(i)}async exportData(){if(Mi()){const{csvImportExportService:e}=await vi(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>_s);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();return t.data?{csvData:t.data,exportDate:(new Date).toISOString()}:{}}return _i.exportData()}async importData(e){if(Mi()){if(e.csvData&&"string"==typeof e.csvData){const{csvImportExportService:e}=await vi(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>_s);return{csvImportExportService:e}},void 0,import.meta.url);return console.log("CSV import for mobile needs full implementation"),!1}return _i.importData(e)}return _i.importData(e)}async migrateFromDesktop(){if(!Mi())return console.warn("Migration should only be called on mobile platform"),!1;try{if((await Wi.getUsers()).length>0)return console.log("CSV storage already has data, skipping migration"),!0;const e={users:localStorage.getItem("smartboutique_users"),products:localStorage.getItem("smartboutique_products"),sales:localStorage.getItem("smartboutique_sales"),debts:localStorage.getItem("smartboutique_debts"),expenses:localStorage.getItem("smartboutique_expenses"),settings:localStorage.getItem("smartboutique_settings"),currentUser:localStorage.getItem("smartboutique_currentUser")};let t=!1;return e.users&&(await Wi.setUsers(JSON.parse(e.users)),t=!0),e.products&&(await Wi.setProducts(JSON.parse(e.products)),t=!0),e.sales&&(await Wi.setSales(JSON.parse(e.sales)),t=!0),e.debts&&(await Wi.setDebts(JSON.parse(e.debts)),t=!0),e.expenses&&(await Wi.setExpenses(JSON.parse(e.expenses)),t=!0),e.settings&&(await Wi.setSettings(JSON.parse(e.settings)),t=!0),e.currentUser&&(await Wi.setCurrentUser(JSON.parse(e.currentUser)),t=!0),t?console.log("Successfully migrated data from desktop to mobile"):console.log("No desktop data found to migrate"),!0}catch(er){return console.error("Error during migration:",er),!1}}};const is=new class{constructor(){n(this,"currentUser",null),n(this,"initialized",!1)}async initialize(){this.initialized||(this.currentUser=await ns.getCurrentUser(),this.initialized=!0)}async login(e,t){await this.initialize();const n=(await ns.getUsers()).find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,await ns.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}async logout(){this.currentUser=null,await ns.remove("currentUser")}getCurrentUser(){return this.currentUser}async getCurrentUserAsync(){return await this.initialize(),this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1,canViewEmployeePayments:!0,canManageEmployeePayments:!0};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1,canViewEmployeePayments:!1,canManageEmployeePayments:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;case"/employee-payments":return t.canViewEmployeePayments;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewEmployeePayments&&t.push({label:"Paiements Employés",path:"/employee-payments",icon:"Payment"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}async updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=await ns.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,await ns.setUsers(t),this.currentUser=i,await ns.setCurrentUser(i),{success:!0}}catch(er){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}async changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}},ss=({currentUser:e,onLogout:t})=>{var n,U;const[T,F]=Dt.useState(!1),k=Jn(),R=Xn(),M=e?is.getUserPermissions():null,I=[{label:"Inventaire",path:"/products",icon:i.jsx(v,{}),permission:"canViewProducts"},{label:"Ventes",path:"/sales",icon:i.jsx(b,{}),permission:"canViewSales"},{label:"Dettes",path:"/debts",icon:i.jsx(S,{}),permission:"canViewDebts"},{label:"Plus",path:"/more",icon:i.jsx(D,{}),permission:null}],A=[{label:"Tableau de bord",path:"/dashboard",icon:i.jsx(f,{}),permission:"canViewDashboard"},{label:"Dépenses",path:"/expenses",icon:i.jsx(C,{}),permission:"canViewExpenses"},{label:"Rapports",path:"/reports",icon:i.jsx(w,{}),permission:"canViewReports"},{label:"Utilisateurs",path:"/users",icon:i.jsx(P,{}),permission:"canViewUsers"},{label:"Paramètres",path:"/settings",icon:i.jsx(E,{}),permission:"canViewSettings"}],N=e=>!e||!M||M[e];return i.jsxs(s,{sx:{display:"flex",flexDirection:"column",height:"100vh"},children:[i.jsx(r,{position:"fixed",sx:{zIndex:e=>e.zIndex.drawer+1},children:i.jsxs(a,{children:[i.jsx(o,{variant:"h6",component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(l,{sx:{bgcolor:"secondary.main",width:32,height:32},children:(null==(n=null==e?void 0:e.nom)?void 0:n.charAt(0))||"U"})]})}),i.jsx(s,{component:"main",sx:{flexGrow:1,pt:8,pb:7,overflow:"auto"},children:i.jsx(ai,{})}),i.jsx(c,{sx:{position:"fixed",bottom:0,left:0,right:0},elevation:3,children:i.jsx(d,{value:(()=>{const e=R.pathname;return I.find(t=>t.path===e)?e:"/more"})(),onChange:(e,t)=>{var n;"/more"===(n=t)?F(!0):k(n)},showLabels:!0,children:I.map(e=>N(e.permission)&&i.jsx(u,{label:e.label,value:e.path,icon:e.icon},e.path))})}),i.jsxs(m,{anchor:"right",open:T,onClose:()=>F(!1),PaperProps:{sx:{width:280}},children:[i.jsx(a,{}),i.jsx(s,{sx:{p:2},children:i.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:2},children:[i.jsx(l,{sx:{bgcolor:"primary.main",mr:2},children:(null==(U=null==e?void 0:e.nom)?void 0:U.charAt(0))||"U"}),i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle1",fontWeight:"bold",children:(null==e?void 0:e.nom)||"Utilisateur"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:(null==e?void 0:e.role)||"Rôle"})]})]})}),i.jsx(h,{}),i.jsxs(p,{children:[A.map(e=>N(e.permission)&&i.jsxs(x,{onClick:()=>{return t=e.path,k(t),void F(!1);var t},sx:{cursor:"pointer"},children:[i.jsx(g,{children:e.icon}),i.jsx(y,{primary:e.label})]},e.path)),i.jsx(h,{sx:{my:1}}),i.jsxs(x,{onClick:()=>{t(),F(!1)},sx:{cursor:"pointer"},children:[i.jsx(g,{children:i.jsx(j,{})}),i.jsx(y,{primary:"Déconnexion"})]})]})]})]})},rs={lessThanXSeconds:{one:"moins d’une seconde",other:"moins de {{count}} secondes"},xSeconds:{one:"1 seconde",other:"{{count}} secondes"},halfAMinute:"30 secondes",lessThanXMinutes:{one:"moins d’une minute",other:"moins de {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"environ 1 heure",other:"environ {{count}} heures"},xHours:{one:"1 heure",other:"{{count}} heures"},xDays:{one:"1 jour",other:"{{count}} jours"},aboutXWeeks:{one:"environ 1 semaine",other:"environ {{count}} semaines"},xWeeks:{one:"1 semaine",other:"{{count}} semaines"},aboutXMonths:{one:"environ 1 mois",other:"environ {{count}} mois"},xMonths:{one:"1 mois",other:"{{count}} mois"},aboutXYears:{one:"environ 1 an",other:"environ {{count}} ans"},xYears:{one:"1 an",other:"{{count}} ans"},overXYears:{one:"plus d’un an",other:"plus de {{count}} ans"},almostXYears:{one:"presqu’un an",other:"presque {{count}} ans"}},as={date:wt({formats:{full:"EEEE d MMMM y",long:"d MMMM y",medium:"d MMM y",short:"dd/MM/y"},defaultWidth:"full"}),time:wt({formats:{full:"HH:mm:ss zzzz",long:"HH:mm:ss z",medium:"HH:mm:ss",short:"HH:mm"},defaultWidth:"full"}),dateTime:wt({formats:{full:"{{date}} 'à' {{time}}",long:"{{date}} 'à' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},os={lastWeek:"eeee 'dernier à' p",yesterday:"'hier à' p",today:"'aujourd’hui à' p",tomorrow:"'demain à' p'",nextWeek:"eeee 'prochain à' p",other:"P"},ls=["MMM","MMMM"],cs={code:"fr",formatDistance:(e,t,n)=>{let i;const s=rs[e];return i="string"==typeof s?s:1===t?s.one:s.other.replace("{{count}}",String(t)),(null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"dans "+i:"il y a "+i:i},formatLong:as,formatRelative:(e,t,n,i)=>os[e],localize:{preprocessor:(e,t)=>{if(1===e.getDate())return t;return t.some(e=>e.isToken&&ls.includes(e.value))?t.map(e=>e.isToken&&"do"===e.value?{isToken:!0,value:"d"}:e):t},ordinalNumber:(e,t)=>{const n=Number(e),i=null==t?void 0:t.unit;if(0===n)return"0";let s;return s=1===n?i&&["year","week","hour","minute","second"].includes(i)?"ère":"er":"ème",n+s},era:Pt({values:{narrow:["av. J.-C","ap. J.-C"],abbreviated:["av. J.-C","ap. J.-C"],wide:["avant Jésus-Christ","après Jésus-Christ"]},defaultWidth:"wide"}),quarter:Pt({values:{narrow:["T1","T2","T3","T4"],abbreviated:["1er trim.","2ème trim.","3ème trim.","4ème trim."],wide:["1er trimestre","2ème trimestre","3ème trimestre","4ème trimestre"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:Pt({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["janv.","févr.","mars","avr.","mai","juin","juil.","août","sept.","oct.","nov.","déc."],wide:["janvier","février","mars","avril","mai","juin","juillet","août","septembre","octobre","novembre","décembre"]},defaultWidth:"wide"}),day:Pt({values:{narrow:["D","L","M","M","J","V","S"],short:["di","lu","ma","me","je","ve","sa"],abbreviated:["dim.","lun.","mar.","mer.","jeu.","ven.","sam."],wide:["dimanche","lundi","mardi","mercredi","jeudi","vendredi","samedi"]},defaultWidth:"wide"}),dayPeriod:Pt({values:{narrow:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"mat.",afternoon:"ap.m.",evening:"soir",night:"mat."},abbreviated:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"matin",afternoon:"après-midi",evening:"soir",night:"matin"},wide:{am:"AM",pm:"PM",midnight:"minuit",noon:"midi",morning:"du matin",afternoon:"de l’après-midi",evening:"du soir",night:"du matin"}},defaultWidth:"wide"})},match:{ordinalNumber:Ut({matchPattern:/^(\d+)(ième|ère|ème|er|e)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e)}),era:Et({matchPatterns:{narrow:/^(av\.J\.C|ap\.J\.C|ap\.J\.-C)/i,abbreviated:/^(av\.J\.-C|av\.J-C|apr\.J\.-C|apr\.J-C|ap\.J-C)/i,wide:/^(avant Jésus-Christ|après Jésus-Christ)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^av/i,/^ap/i]},defaultParseWidth:"any"}),quarter:Et({matchPatterns:{narrow:/^T?[1234]/i,abbreviated:/^[1234](er|ème|e)? trim\.?/i,wide:/^[1234](er|ème|e)? trimestre/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:Et({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(janv|févr|mars|avr|mai|juin|juill|juil|août|sept|oct|nov|déc)\.?/i,wide:/^(janvier|février|mars|avril|mai|juin|juillet|août|septembre|octobre|novembre|décembre)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^av/i,/^ma/i,/^juin/i,/^juil/i,/^ao/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:Et({matchPatterns:{narrow:/^[lmjvsd]/i,short:/^(di|lu|ma|me|je|ve|sa)/i,abbreviated:/^(dim|lun|mar|mer|jeu|ven|sam)\.?/i,wide:/^(dimanche|lundi|mardi|mercredi|jeudi|vendredi|samedi)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^d/i,/^l/i,/^m/i,/^m/i,/^j/i,/^v/i,/^s/i],any:[/^di/i,/^lu/i,/^ma/i,/^me/i,/^je/i,/^ve/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:Et({matchPatterns:{narrow:/^(a|p|minuit|midi|mat\.?|ap\.?m\.?|soir|nuit)/i,any:/^([ap]\.?\s?m\.?|du matin|de l'après[-\s]midi|du soir|de la nuit)/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^min/i,noon:/^mid/i,morning:/mat/i,afternoon:/ap/i,evening:/soir/i,night:/nuit/i}},defaultParseWidth:"any"})},options:{weekStartsOn:1,firstWeekContainsDate:4}};const ds=new class{constructor(){n(this,"currentUser",null),this.currentUser=_i.getCurrentUser()}login(e,t){const n=_i.getUsers().find(n=>n.email===e&&n.motDePasse===t&&n.actif);return n?(this.currentUser=n,_i.setCurrentUser(n),{success:!0,user:n}):{success:!1,message:"Email ou mot de passe incorrect"}}logout(){this.currentUser=null,_i.remove("currentUser")}getCurrentUser(){return this.currentUser}isAuthenticated(){return null!==this.currentUser}getUserPermissions(e){var t;switch(e||(null==(t=this.currentUser)?void 0:t.role)){case"super_admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!0,canViewSettings:!0,canManageSettings:!0,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!0};case"admin":return{canViewDashboard:!0,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!0,canManageDebts:!0,canViewReports:!0,canViewUsers:!0,canManageUsers:!1,canViewSettings:!0,canManageSettings:!1,canViewExpenses:!0,canManageExpenses:!0,canViewFinancials:!0,canViewRevenue:!1};case"employee":return{canViewDashboard:!1,canViewProducts:!0,canManageProducts:!0,canViewSales:!0,canManageSales:!0,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1};default:return{canViewDashboard:!1,canViewProducts:!1,canManageProducts:!1,canViewSales:!1,canManageSales:!1,canViewDebts:!1,canManageDebts:!1,canViewReports:!1,canViewUsers:!1,canManageUsers:!1,canViewSettings:!1,canManageSettings:!1,canViewExpenses:!1,canManageExpenses:!1,canViewFinancials:!1,canViewRevenue:!1}}}hasPermission(e){if(!this.currentUser)return!1;return this.getUserPermissions()[e]}hasRole(e){return!!this.currentUser&&e.includes(this.currentUser.role)}canAccessRoute(e){if(!this.currentUser)return!1;const t=this.getUserPermissions();switch(e){case"/dashboard":return t.canViewDashboard;case"/products":return t.canViewProducts;case"/sales":return t.canViewSales;case"/debts":return t.canViewDebts;case"/reports":return t.canViewReports;case"/users":return t.canViewUsers;case"/settings":return t.canViewSettings;case"/expenses":return t.canViewExpenses;default:return!0}}getNavigationItems(){if(!this.currentUser)return[];const e=this.getUserPermissions(),t=[];return e.canViewDashboard&&t.push({label:"Tableau de bord",path:"/dashboard",icon:"Dashboard"}),e.canViewProducts&&t.push({label:"Inventaire",path:"/products",icon:"Inventory"}),e.canViewSales&&t.push({label:"Ventes",path:"/sales",icon:"PointOfSale"}),e.canViewDebts&&t.push({label:"Dettes",path:"/debts",icon:"AccountBalance"}),e.canViewExpenses&&t.push({label:"Dépenses",path:"/expenses",icon:"Receipt"}),e.canViewReports&&t.push({label:"Rapports",path:"/reports",icon:"Assessment"}),e.canViewUsers&&t.push({label:"Utilisateurs",path:"/users",icon:"People"}),e.canViewSettings&&t.push({label:"Paramètres",path:"/settings",icon:"Settings"}),t}updateProfile(e){if(!this.currentUser)return{success:!1,message:"Utilisateur non connecté"};try{const t=_i.getUsers(),n=t.findIndex(e=>e.id===this.currentUser.id);if(-1===n)return{success:!1,message:"Utilisateur non trouvé"};const i={...t[n],...e};return t[n]=i,_i.setUsers(t),this.currentUser=i,_i.setCurrentUser(i),{success:!0}}catch(er){return{success:!1,message:"Erreur lors de la mise à jour du profil"}}}changePassword(e,t){return this.currentUser?this.currentUser.motDePasse!==e?{success:!1,message:"Mot de passe actuel incorrect"}:t.length<6?{success:!1,message:"Le nouveau mot de passe doit contenir au moins 6 caractères"}:this.updateProfile({motDePasse:t}):{success:!1,message:"Utilisateur non connecté"}}};const us=new class{constructor(){n(this,"STORAGE_KEY","notifications"),n(this,"LOW_STOCK_THRESHOLD",5),n(this,"notifications",[]),n(this,"listeners",[]),this.loadNotifications()}loadNotifications(){try{const e=localStorage.getItem(this.STORAGE_KEY);this.notifications=e?JSON.parse(e):[]}catch(er){console.error("Erreur lors du chargement des notifications:",er),this.notifications=[]}}saveNotifications(){try{localStorage.setItem(this.STORAGE_KEY,JSON.stringify(this.notifications)),this.notifyListeners()}catch(er){console.error("Erreur lors de la sauvegarde des notifications:",er)}}notifyListeners(){this.listeners.forEach(e=>e(this.notifications))}subscribe(e){return this.listeners.push(e),e(this.notifications),()=>{const t=this.listeners.indexOf(e);t>-1&&this.listeners.splice(t,1)}}getNotifications(){return[...this.notifications]}getUnreadCount(){return this.notifications.filter(e=>!e.lu).length}markAsRead(e){const t=this.notifications.find(t=>t.id===e);t&&!t.lu&&(t.lu=!0,this.saveNotifications())}markAllAsRead(){let e=!1;this.notifications.forEach(t=>{t.lu||(t.lu=!0,e=!0)}),e&&this.saveNotifications()}deleteNotification(e){const t=this.notifications.findIndex(t=>t.id===e);t>-1&&(this.notifications.splice(t,1),this.saveNotifications())}clearAll(){this.notifications=[],this.saveNotifications()}shouldReceiveStockNotifications(e){if(!e)return!1;const t=ds.getUserPermissions();return"admin"===e.role||"super_admin"===e.role||!0===(null==t?void 0:t.canManageProducts)}calculateSuggestedReorder(e){return Math.max(3*e.stockMin,20)-e.stock}checkLowStock(e){const t=ds.getCurrentUser();if(!this.shouldReceiveStockNotifications(t))return;e.filter(e=>e.stock>0&&e.stock<=this.LOW_STOCK_THRESHOLD).forEach(e=>{this.notifications.find(t=>"warning"===t.type&&t.titre.includes(e.nom)&&t.message.includes("stock bas")&&!t.lu&&Date.now()-new Date(t.dateCreation).getTime()<864e5)||this.createLowStockNotification(e)})}createLowStockNotification(e){const t=this.calculateSuggestedReorder(e),n={id:`stock-${e.id}-${Date.now()}`,type:"warning",titre:`Stock bas: ${e.nom}`,message:`Le produit "${e.nom}" a un stock critique de ${e.stock} unité(s). Stock minimum: ${e.stockMin}. Quantité suggérée à commander: ${t} unité(s).`,dateCreation:(new Date).toISOString(),lu:!1,productId:e.id,productName:e.nom,currentStock:e.stock,minimumStock:e.stockMin,suggestedReorder:t};this.notifications.unshift(n),this.saveNotifications(),this.showBrowserNotification(n)}showBrowserNotification(e){"Notification"in window&&"granted"===Notification.permission&&new Notification(`SmartBoutique - ${e.titre}`,{body:`Stock actuel: ${e.currentStock} unité(s). Commande suggérée: ${e.suggestedReorder} unité(s).`,icon:"/favicon.ico",tag:`stock-${e.productId}`})}async requestNotificationPermission(){if(!("Notification"in window))return!1;if("granted"===Notification.permission)return!0;if("denied"===Notification.permission)return!1;return"granted"===await Notification.requestPermission()}createNotification(e,t,n){const i={id:`notif-${Date.now()}`,type:e,titre:t,message:n,dateCreation:(new Date).toISOString(),lu:!1};this.notifications.unshift(i),this.saveNotifications()}},ms=({color:e="inherit"})=>{const[t,n]=Dt.useState(null),{notifications:r,unreadCount:a,markAsRead:l,markAllAsRead:d,deleteNotification:u,clearAll:m,requestPermission:j}=(()=>{const[e,t]=Dt.useState([]),[n,i]=Dt.useState(0);return Dt.useEffect(()=>us.subscribe(e=>{t(e),i(us.getUnreadCount())}),[]),{notifications:e,unreadCount:n,markAsRead:e=>{us.markAsRead(e)},markAllAsRead:()=>{us.markAllAsRead()},deleteNotification:e=>{us.deleteNotification(e)},clearAll:()=>{us.clearAll()},requestPermission:async()=>await us.requestNotificationPermission()}})(),b=e=>{switch(e){case"info":default:return i.jsx(B,{color:"info"});case"warning":return i.jsx($,{color:"warning"});case"error":return i.jsx(_,{color:"error"});case"success":return i.jsx(z,{color:"success"})}},S=e=>{switch(e){case"info":return"info";case"warning":return"warning";case"error":return"error";case"success":return"success";default:return"default"}},D=Boolean(t),f=D?"notification-popover":void 0;return i.jsxs(i.Fragment,{children:[i.jsx(U,{title:"Notifications",children:i.jsx(T,{color:e,onClick:e=>{n(e.currentTarget)},children:i.jsx(F,{badgeContent:a,color:"error",children:a>0?i.jsx(k,{}):i.jsx(R,{})})})}),i.jsx(M,{id:f,open:D,anchorEl:t,onClose:()=>{n(null)},anchorOrigin:{vertical:"bottom",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"right"},children:i.jsxs(c,{sx:{width:400,maxHeight:500},children:[i.jsxs(s,{sx:{p:2,borderBottom:"1px solid",borderColor:"divider"},children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",children:[i.jsx(o,{variant:"h6",children:"Notifications"}),a>0&&i.jsx(I,{label:`${a} non lue(s)`,color:"primary",size:"small"})]}),r.length>0&&i.jsxs(s,{sx:{mt:1,display:"flex",gap:1},children:[a>0&&i.jsx(A,{size:"small",startIcon:i.jsx(N,{}),onClick:d,children:"Tout marquer lu"}),i.jsx(A,{size:"small",startIcon:i.jsx(L,{}),onClick:m,color:"error",children:"Tout effacer"})]})]}),"Notification"in window&&"default"===Notification.permission&&i.jsx(O,{severity:"info",sx:{m:1},action:i.jsx(A,{size:"small",onClick:async()=>{await j()},children:"Activer"}),children:"Activez les notifications du navigateur pour les alertes de stock"}),0===r.length?i.jsxs(s,{sx:{p:3,textAlign:"center"},children:[i.jsx(R,{sx:{fontSize:48,color:"text.secondary",mb:1}}),i.jsx(o,{variant:"body2",color:"text.secondary",children:"Aucune notification"})]}):i.jsx(p,{sx:{maxHeight:350,overflow:"auto"},children:r.map((e,t)=>i.jsxs(Ct.Fragment,{children:[i.jsxs(x,{button:!0,onClick:()=>(e=>{e.lu||l(e.id)})(e),sx:{bgcolor:e.lu?"transparent":"action.hover","&:hover":{bgcolor:"action.selected"}},children:[i.jsx(g,{children:e.titre.includes("Stock bas")?i.jsx(v,{color:"warning"}):b(e.type)}),i.jsx(y,{primary:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"subtitle2",sx:{fontWeight:e.lu?"normal":"bold",flex:1},children:e.titre}),i.jsx(I,{label:e.type,size:"small",color:S(e.type),variant:"outlined"})]}),secondary:i.jsxs(s,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mb:.5},children:e.message}),i.jsx(o,{variant:"caption",color:"text.secondary",children:Tt(new Date(e.dateCreation),"dd/MM/yyyy HH:mm",{locale:cs})})]})}),i.jsx(V,{children:i.jsx(U,{title:"Supprimer",children:i.jsx(T,{edge:"end",size:"small",onClick:t=>{return n=t,i=e.id,n.stopPropagation(),void u(i);var n,i},children:i.jsx(q,{fontSize:"small"})})})})]}),t<r.length-1&&i.jsx(h,{})]},e.id))})]})})]})},hs=240,ps=({currentUser:e,onLogout:t})=>{var n;if(Mi())return i.jsx(ss,{currentUser:e,onLogout:t});const[c,d]=Dt.useState(!1),[u,F]=Dt.useState(null),k=Jn(),R=Xn(),M=()=>{d(!c)},I=()=>{F(null)},A=is.getNavigationItems(),N=e=>{switch(e){case"Dashboard":default:return i.jsx(f,{});case"Inventory":return i.jsx(v,{});case"PointOfSale":return i.jsx(b,{});case"AccountBalance":return i.jsx(S,{});case"Receipt":return i.jsx(C,{});case"Payment":return i.jsx(Y,{});case"Assessment":return i.jsx(w,{});case"People":return i.jsx(P,{});case"Settings":return i.jsx(E,{})}},L=i.jsxs("div",{children:[i.jsx(a,{children:i.jsxs(s,{display:"flex",alignItems:"center",width:"100%",children:[i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:"SmartBoutique"}),i.jsx(T,{onClick:M,sx:{display:{sm:"none"}},children:i.jsx(W,{})})]})}),i.jsx(h,{}),i.jsx(p,{children:A.map(e=>i.jsx(x,{disablePadding:!0,children:i.jsxs(X,{selected:R.pathname===e.path,onClick:()=>{k(e.path),d(!1)},children:[i.jsx(g,{children:N(e.icon)}),i.jsx(y,{primary:e.label})]})},e.path))})]});return i.jsxs(s,{sx:{display:"flex"},children:[i.jsx(r,{position:"fixed",sx:{width:{sm:"calc(100% - 240px)"},ml:{sm:"240px"}},children:i.jsxs(a,{children:[i.jsx(T,{color:"inherit","aria-label":"open drawer",edge:"start",onClick:M,sx:{mr:2,display:{sm:"none"}},children:i.jsx(D,{})}),i.jsx(o,{variant:"h6",noWrap:!0,component:"div",sx:{flexGrow:1},children:(null==(n=A.find(e=>e.path===R.pathname))?void 0:n.label)||"SmartBoutique"}),i.jsx(ms,{color:"inherit"}),i.jsx(U,{title:"Profil utilisateur",children:i.jsx(T,{color:"inherit",onClick:e=>{F(e.currentTarget)},sx:{ml:1},children:i.jsx(l,{sx:{width:32,height:32,bgcolor:"secondary.main"},children:null==e?void 0:e.nom.charAt(0).toUpperCase()})})}),i.jsxs(Q,{anchorEl:u,open:Boolean(u),onClose:I,onClick:I,children:[i.jsx(J,{disabled:!0,children:i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",children:null==e?void 0:e.nom}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["super_admin"===(null==e?void 0:e.role)&&"Super Administrateur","admin"===(null==e?void 0:e.role)&&"Administrateur","employee"===(null==e?void 0:e.role)&&"Employé"]})]})}),i.jsx(h,{}),i.jsxs(J,{onClick:()=>k("/settings"),children:[i.jsx(g,{children:i.jsx(H,{fontSize:"small"})}),"Mon Profil"]}),i.jsxs(J,{onClick:()=>{I(),t(),k("/login")},children:[i.jsx(g,{children:i.jsx(j,{fontSize:"small"})}),"Déconnexion"]})]})]})}),i.jsxs(s,{component:"nav",sx:{width:{sm:hs},flexShrink:{sm:0}},children:[i.jsx(m,{variant:"temporary",open:c,onClose:M,ModalProps:{keepMounted:!0},sx:{display:{xs:"block",sm:"none"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:hs}},children:L}),i.jsx(m,{variant:"permanent",sx:{display:{xs:"none",sm:"block"},"& .MuiDrawer-paper":{boxSizing:"border-box",width:hs}},open:!0,children:L})]}),i.jsxs(s,{component:"main",sx:{flexGrow:1,p:3,width:{sm:"calc(100% - 240px)"},minHeight:"100vh",backgroundColor:"background.default"},children:[i.jsx(a,{}),i.jsx(ai,{})]})]})},xs=({children:e,requiredPermission:t,requiredRole:n})=>is.getCurrentUser()?n&&!is.hasRole(n)?i.jsx(s,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(K,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Contactez votre administrateur pour obtenir l'accès requis."})]})}):t&&!is.hasPermission(t)?i.jsx(s,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"60vh",children:i.jsxs(c,{sx:{p:4,textAlign:"center",maxWidth:400},children:[i.jsx(K,{sx:{fontSize:64,color:"error.main",mb:2}}),i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Accès Refusé"}),i.jsx(o,{variant:"body1",color:"text.secondary",paragraph:!0,children:"Vous n'avez pas les permissions nécessaires pour accéder à cette page."}),i.jsx(O,{severity:"warning",sx:{mt:2},children:"Cette fonctionnalité est réservée aux utilisateurs autorisés."})]})}):i.jsx(i.Fragment,{children:e}):i.jsx(ri,{to:"/login",replace:!0}),gs=({onLogin:e})=>{const[t,n]=Dt.useState(""),[r,a]=Dt.useState(""),[c,d]=Dt.useState(!1),[u,m]=Dt.useState(""),[h,p]=Dt.useState(!1),x=async t=>{p(!0),m("");let n={email:"",password:""};switch(t){case"admin":n={email:"<EMAIL>",password:"admin123"};break;case"manager":n={email:"<EMAIL>",password:"manager123"};break;case"employee":n={email:"<EMAIL>",password:"employee123"}}try{const t=await is.login(n.email,n.password);t.success&&t.user?e(t.user):m(t.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}};return i.jsx(G,{maxWidth:"sm",sx:{height:"100vh",display:"flex",alignItems:"center"},children:i.jsxs(s,{sx:{width:"100%",px:2},children:[i.jsxs(s,{sx:{textAlign:"center",mb:4},children:[i.jsx(l,{sx:{width:80,height:80,bgcolor:"primary.main",mx:"auto",mb:2},children:i.jsx(Z,{sx:{fontSize:40}})}),i.jsx(o,{variant:"h4",component:"h1",fontWeight:"bold",color:"primary",children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",sx:{mt:1},children:"Gestion de boutique mobile"})]}),i.jsx(ee,{elevation:4,sx:{borderRadius:3},children:i.jsxs(te,{sx:{p:4},children:[i.jsx(o,{variant:"h5",component:"h2",textAlign:"center",sx:{mb:3},children:"Connexion"}),u&&i.jsx(O,{severity:"error",sx:{mb:3},children:u}),i.jsxs(s,{component:"form",onSubmit:async n=>{n.preventDefault(),m(""),p(!0);try{const n=await is.login(t,r);n.success&&n.user?e(n.user):m(n.message||"Erreur de connexion")}catch(i){m("Erreur de connexion. Veuillez réessayer.")}finally{p(!1)}},children:[i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:t,onChange:e=>n(e.target.value),required:!0,sx:{mb:3},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(se,{color:"action"})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"email",style:{fontSize:"16px"}}}),i.jsx(ne,{fullWidth:!0,label:"Mot de passe",type:c?"text":"password",value:r,onChange:e=>a(e.target.value),required:!0,sx:{mb:4},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(K,{color:"action"})}),endAdornment:i.jsx(ie,{position:"end",children:i.jsx(T,{onClick:()=>{d(!c)},edge:"end",size:"large",children:c?i.jsx(re,{}):i.jsx(ae,{})})}),style:{fontSize:"16px"}},inputProps:{autoComplete:"current-password",style:{fontSize:"16px"}}}),i.jsx(A,{type:"submit",fullWidth:!0,variant:"contained",size:"large",disabled:h,sx:{py:2,fontSize:"1.1rem",fontWeight:"bold",borderRadius:2,mb:3},children:h?"Connexion...":"Se connecter"})]}),i.jsxs(s,{sx:{mt:3},children:[i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mb:2},children:"Connexion rapide (Démo)"}),i.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(A,{variant:"outlined",size:"large",onClick:()=>x("admin"),disabled:h,sx:{py:1.5},children:"Super Admin"}),i.jsx(A,{variant:"outlined",size:"large",onClick:()=>x("manager"),disabled:h,sx:{py:1.5},children:"Gestionnaire"}),i.jsx(A,{variant:"outlined",size:"large",onClick:()=>x("employee"),disabled:h,sx:{py:1.5},children:"Employé"})]})]})]})}),i.jsx(o,{variant:"body2",color:"text.secondary",textAlign:"center",sx:{mt:3},children:"Version Mobile • SmartBoutique 2024"})]})})},ys=({onLogin:e})=>{if(Mi())return i.jsx(gs,{onLogin:e});const[t,n]=Dt.useState(""),[r,a]=Dt.useState(""),[c,d]=Dt.useState(""),[u,m]=Dt.useState(!1),p=(e,t)=>{n(e),a(t)};return i.jsx(G,{component:"main",maxWidth:"sm",children:i.jsxs(s,{sx:{minHeight:"100vh",display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",py:4},children:[i.jsxs(s,{sx:{mb:4,textAlign:"center"},children:[i.jsx(l,{sx:{mx:"auto",mb:2,bgcolor:"primary.main",width:64,height:64},children:i.jsx(Z,{sx:{fontSize:32}})}),i.jsx(o,{component:"h1",variant:"h4",gutterBottom:!0,children:"SmartBoutique"}),i.jsx(o,{variant:"subtitle1",color:"text.secondary",children:"Système de Gestion de Boutique"})]}),i.jsx(ee,{sx:{width:"100%",maxWidth:400},children:i.jsxs(te,{sx:{p:4},children:[i.jsxs(s,{sx:{display:"flex",alignItems:"center",mb:3},children:[i.jsx(l,{sx:{mr:2,bgcolor:"secondary.main"},children:i.jsx(oe,{})}),i.jsx(o,{component:"h2",variant:"h5",children:"Connexion"})]}),c&&i.jsx(O,{severity:"error",sx:{mb:2},children:c}),i.jsxs(s,{component:"form",onSubmit:async n=>{n.preventDefault(),d(""),m(!0);try{const n=await is.login(t,r);n.success&&n.user?e(n.user):d(n.message||"Erreur de connexion")}catch(i){d("Une erreur est survenue lors de la connexion")}finally{m(!1)}},children:[i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,id:"email",label:"Adresse email",name:"email",autoComplete:"email",autoFocus:!0,value:t,onChange:e=>n(e.target.value),disabled:u}),i.jsx(ne,{margin:"normal",required:!0,fullWidth:!0,name:"password",label:"Mot de passe",type:"password",id:"password",autoComplete:"current-password",value:r,onChange:e=>a(e.target.value),disabled:u}),i.jsx(A,{type:"submit",fullWidth:!0,variant:"contained",sx:{mt:3,mb:2},disabled:u,children:u?"Connexion...":"Se connecter"})]}),i.jsx(h,{sx:{my:3},children:i.jsx(I,{label:"Comptes de démonstration",size:"small"})}),i.jsxs(s,{sx:{display:"flex",flexDirection:"column",gap:1},children:[i.jsx(A,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","admin123"),disabled:u,children:"Super Admin (<EMAIL>)"}),i.jsx(A,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","manager123"),disabled:u,children:"Gestionnaire (<EMAIL>)"}),i.jsx(A,{variant:"outlined",size:"small",onClick:()=>p("<EMAIL>","employee123"),disabled:u,children:"Employé (<EMAIL>)"})]})]})}),i.jsxs(s,{sx:{mt:4,textAlign:"center"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"© 2024 SmartBoutique. Tous droits réservés."}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Version 1.0.0"})]})]})})},js=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,vs=({analytics:e,products:t,isLoading:n=!1})=>{if(n)return i.jsxs(s,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Revenus - Analyse des Bénéfices"}),i.jsx(le,{})]});if(0===e.totalProductsWithProfit)return i.jsxs(s,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,children:"Revenus - Analyse des Bénéfices"}),i.jsx(O,{severity:"info",children:"Aucun produit avec prix d'achat et de vente configurés. Ajoutez des prix d'achat aux produits pour voir l'analyse des revenus."})]});const r=Object.entries(e.categoryBreakdown);return i.jsxs(s,{children:[i.jsx(o,{variant:"h5",gutterBottom:!0,sx:{mb:3},children:"Revenus - Analyse des Bénéfices"}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Revenus Potentiels"}),i.jsx(o,{variant:"h6",children:js(e.totalInventoryRevenueCDF,"CDF")}),i.jsx(o,{variant:"body2",color:"primary",children:js(e.totalInventoryRevenueUSD,"USD")})]}),i.jsx(v,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Revenus Réalisés"}),i.jsx(o,{variant:"h6",children:js(e.realizedRevenueCDF,"CDF")}),i.jsx(o,{variant:"body2",color:"success.main",children:js(e.realizedRevenueUSD,"USD")})]}),i.jsx(de,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),i.jsx(o,{variant:"h6",children:r.length}),i.jsx(o,{variant:"body2",color:"textSecondary",children:"avec revenus"})]}),i.jsx(ue,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Produits les Plus Rentables"}),i.jsx(te,{children:i.jsx(he,{children:i.jsxs(pe,{size:"small",children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Produit"}),i.jsx(ye,{align:"right",children:"Revenus Potentiels"})]})}),i.jsx(je,{children:e.topProducts.map(e=>{const t=(e.beneficeUnitaireCDF||0)*e.stock;return i.jsxs(ge,{children:[i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"body2",fontWeight:"medium",children:e.nom}),i.jsxs(o,{variant:"caption",color:"textSecondary",children:["Stock: ",e.stock]})]})}),i.jsx(ye,{align:"right",children:i.jsx(o,{variant:"body2",children:js(t,"CDF")})})]},e.id)})})]})})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Revenus par Catégorie"}),i.jsx(te,{children:i.jsx(he,{children:i.jsxs(pe,{size:"small",children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Catégorie"}),i.jsx(ye,{align:"right",children:"Produits"}),i.jsx(ye,{align:"right",children:"Revenus (CDF)"})]})}),i.jsx(je,{children:r.sort(([,e],[,t])=>t.revenueCDF-e.revenueCDF).map(([e,t])=>i.jsxs(ge,{children:[i.jsx(ye,{children:i.jsx(I,{label:e,size:"small"})}),i.jsx(ye,{align:"right",children:t.productCount}),i.jsxs(ye,{align:"right",children:[i.jsx(o,{variant:"body2",children:js(t.revenueCDF,"CDF")}),i.jsx(o,{variant:"caption",color:"textSecondary",children:js(t.revenueUSD,"USD")})]})]},e))})]})})})]})})]})]})},bs=()=>{const[e,t]=Dt.useState(null),[n,r]=Dt.useState([]),[a,l]=Dt.useState([]),[d,u]=Dt.useState(null),[m,h]=Dt.useState(null),[j,D]=Dt.useState(null),[f,C]=Dt.useState("jour"),w=is.getUserPermissions(),P=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(er){return console.warn("Error parsing date:",e,er),null}};Dt.useEffect(()=>{U()},[]);const E=(e,t)=>{switch(e){case"jour":default:return t.ventesDuJour;case"semaine":return t.ventesDeLaSemaine;case"mois":return t.ventesDuMois}},U=async()=>{const e=await ns.getProducts(),n=await ns.getSales(),i=await ns.getDebts(),s=await ns.getSettings(),a=new Date,o=Ft(a),c=kt(a),d=Rt(a,7),u=Rt(a,30),m=n.filter(e=>{const t=P(e.datevente);return!!t&&(Mt(t,o)&&It(t,c))}),h=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,d)}),p=n.filter(e=>{const t=P(e.datevente);return!!t&&Mt(t,u)}),x=e.filter(e=>e.stock>0),g=e.filter(e=>e.stock<=e.stockMin&&e.stock>0),y=e.filter(e=>0===e.stock),j=i.filter(e=>"active"===e.statut),v=i.filter(e=>"overdue"===e.statut),b=m.reduce((e,t)=>e+t.totalCDF,0),S=h.reduce((e,t)=>e+t.totalCDF,0),f=p.reduce((e,t)=>e+t.totalCDF,0),C=e.reduce((e,t)=>e+t.prixCDF*t.stock,0),E=j.reduce((e,t)=>e+t.montantRestantCDF,0),U={ventesDuJour:{nombreVentes:m.length,revenusCDF:b,revenusUSD:b/s.tauxChangeUSDCDF},ventesDeLaSemaine:{nombreVentes:h.length,revenusCDF:S,revenusUSD:S/s.tauxChangeUSDCDF},ventesDuMois:{nombreVentes:p.length,revenusCDF:f,revenusUSD:f/s.tauxChangeUSDCDF},articlesActifs:x.length,produitsStockBas:g.length,articlesEnRupture:y.length,valeurInventaireCDF:C,valeurInventaireUSD:C/s.tauxChangeUSDCDF,dettesActives:j.length,dettesEnRetard:v.length,montantDettesTotalCDF:E,montantDettesTotalUSD:E/s.tauxChangeUSDCDF};if(t(U),r(g.slice(0,5)),l(n.slice(-5).reverse()),w.canViewRevenue){const t=((e,t)=>{const n=(e=>{let t=0,n=0;return e.forEach(e=>{e.beneficeUnitaireCDF&&e.stock>0&&(t+=e.beneficeUnitaireCDF*e.stock),e.beneficeUnitaireUSD&&e.stock>0&&(n+=e.beneficeUnitaireUSD*e.stock)}),{totalRevenueCDF:t,totalRevenueUSD:n}})(e),i=((e,t)=>{let n=0,i=0;return e.forEach(e=>{e.produits.forEach(e=>{const s=t.find(t=>t.id===e.produitId);s&&s.beneficeUnitaireCDF&&(n+=s.beneficeUnitaireCDF*e.quantite),s&&s.beneficeUnitaireUSD&&(i+=s.beneficeUnitaireUSD*e.quantite)})}),{realizedRevenueCDF:n,realizedRevenueUSD:i}})(t,e),s=(e=>{const t={};return e.forEach(e=>{t[e.categorie]||(t[e.categorie]={revenueCDF:0,revenueUSD:0,productCount:0}),e.beneficeUnitaireCDF&&e.stock>0&&(t[e.categorie].revenueCDF+=e.beneficeUnitaireCDF*e.stock),e.beneficeUnitaireUSD&&e.stock>0&&(t[e.categorie].revenueUSD+=e.beneficeUnitaireUSD*e.stock),t[e.categorie].productCount+=1}),t})(e),r=((e,t=10)=>e.filter(e=>e.beneficeUnitaireCDF&&e.beneficeUnitaireCDF>0).sort((e,t)=>{const n=(e.beneficeUnitaireCDF||0)*e.stock;return(t.beneficeUnitaireCDF||0)*t.stock-n}).slice(0,t))(e,5);return{totalInventoryRevenueCDF:n.totalRevenueCDF,totalInventoryRevenueUSD:n.totalRevenueUSD,realizedRevenueCDF:i.realizedRevenueCDF,realizedRevenueUSD:i.realizedRevenueUSD,categoryBreakdown:s,topProducts:r}})(e,n);D(t)}T(n),F(n)},T=e=>{const t=Array.from({length:14},(e,t)=>{const n=Rt(new Date,13-t);return{date:n,label:Tt(n,"dd/MM",{locale:cs}),sales:0,revenue:0}});e.forEach(e=>{const n=P(e.datevente);if(!n)return;const i=t.find(e=>Tt(e.date,"yyyy-MM-dd")===Tt(n,"yyyy-MM-dd"));i&&(i.sales+=1,i.revenue+=e.totalCDF)}),u({labels:t.map(e=>e.label),datasets:[{label:"Revenus (CDF)",data:t.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]})},F=e=>{const t=e.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+1,e),{});h({labels:["Espèces","Carte","Mobile Money"],datasets:[{data:[t.cash||0,t.card||0,t.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]})},k=e=>0===e.stock?i.jsx(_,{color:"error"}):e.stock<=e.stockMin?i.jsx($,{color:"warning"}):i.jsx(z,{color:"success"}),R=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`;return e?i.jsxs(s,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Tableau de bord"}),w.canViewRevenue&&j&&i.jsx(s,{sx:{mb:4},children:i.jsx(vs,{analytics:j,products:n,isLoading:!j})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",mb:1,children:[i.jsxs(ve,{size:"small",sx:{minWidth:120},children:[i.jsx(be,{children:"Période"}),i.jsxs(Se,{value:f,label:"Période",onChange:e=>C(e.target.value),children:[i.jsx(J,{value:"jour",children:"Jour"}),i.jsx(J,{value:"semaine",children:"Semaine"}),i.jsx(J,{value:"mois",children:"Mois"})]})]}),i.jsx(b,{color:"primary",sx:{fontSize:40}})]}),i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:(e=>{switch(e){case"jour":default:return"Ventes du jour";case"semaine":return"Ventes de la semaine";case"mois":return"Ventes du mois"}})(f)}),i.jsx(o,{variant:"h6",children:E(f,e).nombreVentes}),w.canViewFinancials&&i.jsx(o,{variant:"body2",color:"primary",children:R(E(f,e).revenusCDF,"CDF")})]})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes de la semaine"}),i.jsx(o,{variant:"h6",children:e.ventesDeLaSemaine.nombreVentes}),w.canViewFinancials&&i.jsx(o,{variant:"body2",color:"primary",children:R(e.ventesDeLaSemaine.revenusCDF,"CDF")})]}),i.jsx(De,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Articles actifs"}),i.jsx(o,{variant:"h6",children:e.articlesActifs}),e.produitsStockBas>0&&i.jsx(I,{label:`${e.produitsStockBas} stock bas`,color:"warning",size:"small"})]}),i.jsx(v,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total des Dettes"}),w.canViewFinancials?i.jsxs(i.Fragment,{children:[i.jsx(o,{variant:"h6",color:"error",children:R(e.montantDettesTotalCDF,"CDF")}),i.jsx(o,{variant:"body2",color:"error",children:R(e.montantDettesTotalUSD||0,"USD")})]}):i.jsxs(o,{variant:"h6",children:[e.dettesActives," dettes"]})]}),i.jsx(S,{color:"warning",sx:{fontSize:40}})]})})})})]}),e.articlesEnRupture>0&&i.jsx(ce,{container:!0,spacing:3,sx:{mb:3},children:i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",sx:{mb:2},children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{variant:"h6",component:"div",children:"Articles en rupture"}),i.jsxs(o,{variant:"body2",children:[e.articlesEnRupture," article",e.articlesEnRupture>1?"s":""," en rupture de stock nécessite",e.articlesEnRupture>1?"nt":""," un réapprovisionnement urgent"]})]}),i.jsx(_,{sx:{fontSize:40}})]})})})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Tendance des ventes (14 derniers jours)"}),d&&i.jsx(Vt,{data:d,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Méthodes de paiement"}),m&&i.jsx(qt,{data:m,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})]})})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Produits en stock bas"}),0===n.length?i.jsx(O,{severity:"success",children:"Aucun produit en stock bas"}):i.jsx(p,{dense:!0,children:n.map(e=>i.jsxs(x,{children:[i.jsx(g,{children:k(e)}),i.jsx(y,{primary:e.nom,secondary:`Stock: ${e.stock} (Min: ${e.stockMin})`})]},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(c,{sx:{p:2},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Ventes récentes"}),0===a.length?i.jsx(O,{severity:"info",children:"Aucune vente récente"}):i.jsx(p,{dense:!0,children:a.map(e=>i.jsx(x,{children:i.jsx(y,{primary:w.canViewFinancials?`${e.nomClient} - ${R(e.totalCDF,"CDF")}`:e.nomClient,secondary:(()=>{const t=P(e.datevente);return t?Tt(t,"dd/MM/yyyy HH:mm",{locale:cs}):"Date invalide"})()})},e.id))})]})})]})]}):i.jsx(o,{children:"Chargement..."})},Ss=(e,t)=>{const n=null==e||isNaN(e)?0:e;return"USD"===t?`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${n.toLocaleString("fr-FR")} CDF`},Ds=e=>0===e.stock?"out_of_stock":e.stock<=e.stockMin?"low_stock":"in_stock",fs=()=>"123"+Date.now().toString().slice(-10),Cs=()=>{const e=Date.now().toString(),t=Math.random().toString(36).substr(2,4).toUpperCase();return`SB${e.slice(-8)}${t}`},ws=(e,t)=>e/t,Ps=(e,t)=>Math.round(e/t*100)/100,Es=({label:e,value:t,onChange:n,min:r=0,max:a=1e6,step:l=100,exchangeRate:c,disabled:d=!1,required:u=!1,error:m=!1,helperText:h,showSlider:p=!0,allowUSDInput:x=!0})=>{const[g,y]=Dt.useState("CDF"),[j,v]=Dt.useState(""),[b,S]=Dt.useState(t);Dt.useEffect(()=>{if(S(t),"CDF"===g)v(t.toString());else{const e=ws(t,c);v(e.toFixed(2))}},[t,g,c]);const D=ws(b,c),f=b;return i.jsxs(ve,{fullWidth:!0,disabled:d,children:[i.jsxs(fe,{component:"legend",sx:{mb:1},children:[e," ",u&&"*"]}),i.jsxs(ce,{container:!0,spacing:2,children:[x&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(Ce,{value:g,exclusive:!0,onChange:(e,t)=>{if(t&&t!==g)if(y(t),"USD"===t){const e=ws(b,c);v(e.toFixed(2))}else v(b.toString())},size:"small",disabled:d,children:[i.jsx(we,{value:"CDF",children:"Saisie en CDF"}),i.jsx(we,{value:"USD",children:"Saisie en USD"})]})}),i.jsx(ce,{item:!0,xs:12,md:p?6:12,children:i.jsx(ne,{fullWidth:!0,label:`Montant (${g})`,type:"number",value:j,onChange:e=>{const t=e.target.value;v(t);const i=parseFloat(t)||0;let s;s="CDF"===g?i:((e,t)=>e*t)(i,c),s=Math.max(r,Math.min(a,s)),S(s),n(s)},disabled:d,error:m,helperText:h,inputProps:{min:"CDF"===g?r:ws(r,c),max:"CDF"===g?a:ws(a,c),step:"CDF"===g?l:.01},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:"USD"===g?"$":""}),endAdornment:i.jsx(ie,{position:"end",children:"CDF"===g?"CDF":"USD"})}})}),p&&i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(s,{sx:{px:2,pt:3},children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Curseur (CDF)"}),i.jsx(Pe,{value:b,onChange:(e,t)=>{const i=Array.isArray(t)?t[0]:t;if(S(i),"CDF"===g)v(i.toString());else{const e=ws(i,c);v(e.toFixed(2))}n(i)},min:r,max:a,step:l,disabled:d,valueLabelDisplay:"auto",valueLabelFormat:e=>Ss(e,"CDF")})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(s,{sx:{p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Équivalences:"}),i.jsx(o,{variant:"body1",color:"primary",fontWeight:"medium",children:Ss(f,"CDF")}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["≈ ",Ss(D,"USD")]})]})})]})]})},Us=({value:e,onChange:t,min:n=1,max:r=999999,disabled:a=!1,size:o="small",showButtons:l=!0,allowDirectInput:c=!0,label:d,error:u=!1,helperText:m})=>{const[h,p]=Dt.useState(e.toString());Dt.useEffect(()=>{p(e.toString())},[e]);const x=i=>{const s=i.target.value;if(p(s),""===s)return;const a=parseFloat(s);if(!isNaN(a)){const i=Math.round(a),s=Math.max(n,Math.min(r,i));i>=n&&i<=r?t(i):s!==e&&t(s)}},g=()=>{if(""===h||isNaN(parseFloat(h))){const i=e||n;return p(i.toString()),void(i!==e&&t(i))}const i=Math.round(parseFloat(h)),s=Math.max(n,Math.min(r,i));p(s.toString()),s!==e&&t(s)},y=()=>{const n=Math.min(r,e+1);t(n)},j=()=>{const i=Math.max(n,e-1);t(i)},v=e=>{e.ctrlKey&&["a","c","v","x"].includes(e.key.toLowerCase())||(/[0-9.]/.test(e.key)||["Backspace","Delete","ArrowLeft","ArrowRight","ArrowUp","ArrowDown","Tab","Enter","Home","End","Escape","."].includes(e.key)||e.preventDefault(),"."===e.key&&h.includes(".")&&e.preventDefault())};return!c&&l?i.jsxs(s,{display:"flex",alignItems:"center",gap:.5,children:[i.jsx(T,{size:o,onClick:j,disabled:a||e<=n,children:i.jsx(Ee,{fontSize:o})}),i.jsx(s,{sx:{minWidth:"small"===o?"30px":"40px",textAlign:"center",fontWeight:"medium",fontSize:"small"===o?"0.875rem":"1rem"},children:e}),i.jsx(T,{size:o,onClick:y,disabled:a||e>=r,children:i.jsx(Ue,{fontSize:o})})]}):c&&!l?i.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:v,disabled:a,error:u,helperText:m,inputProps:{min:n,max:r,step:1},sx:{minWidth:"80px"}}):i.jsx(ne,{size:o,label:d,type:"number",value:h,onChange:x,onBlur:g,onKeyPress:v,disabled:a,error:u,helperText:m||(l?"Tapez directement la quantité (recommandé) ou utilisez +/-":"Tapez directement la quantité désirée"),placeholder:"Tapez la quantité...",inputProps:{min:n,max:r,step:1,style:{textAlign:"center",fontSize:"small"===o?"0.875rem":"1rem",fontWeight:500}},InputProps:{startAdornment:l?i.jsx(ie,{position:"start",children:i.jsx(T,{size:o,onClick:j,disabled:a||e<=n,edge:"start",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(Ee,{fontSize:o})})}):void 0,endAdornment:l?i.jsx(ie,{position:"end",children:i.jsx(T,{size:o,onClick:y,disabled:a||e>=r,edge:"end",sx:{opacity:.6,"&:hover":{opacity:.8},transition:"opacity 0.2s ease"},children:i.jsx(Ue,{fontSize:o})})}):void 0},sx:{minWidth:l?"160px":"100px","& .MuiOutlinedInput-root":{"&:hover fieldset":{borderColor:"primary.main"},"&.Mui-focused fieldset":{borderWidth:2,borderColor:"primary.main"},"& input":{cursor:"text","&:focus":{backgroundColor:"rgba(25, 118, 210, 0.04)"}}}}})},Ts=()=>{const[e,t]=Dt.useState([]),[n,r]=Dt.useState([]),[a,l]=Dt.useState([]),[d,u]=Dt.useState(""),[m,h]=Dt.useState(""),[p,x]=Dt.useState("all"),[g,y]=Dt.useState(0),[j,b]=Dt.useState(10),[S,D]=Dt.useState(!1),[f,C]=Dt.useState(null),[w,P]=Dt.useState({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0}),[E,F]=Dt.useState(""),[k,R]=Dt.useState(""),[M,N]=Dt.useState(!1),[L,V]=Dt.useState(""),[B,W]=Dt.useState({tauxChangeUSDCDF:2800}),X=is.getUserPermissions();Dt.useEffect(()=>{Q()},[]);Dt.useEffect(()=>{e.length>0&&us.checkLowStock(e)},[e]),Dt.useEffect(()=>{H()},[e,d,m,p]),Dt.useEffect(()=>{-1===j&&b(a.length||1)},[a.length,j]);const Q=async()=>{try{const e=await ns.getProducts(),n=await ns.getSettings(),i=e.map(e=>{const t=(new Date).toISOString();return{...e,dateCreation:e.dateCreation&&!isNaN(new Date(e.dateCreation).getTime())?e.dateCreation:t,dateModification:e.dateModification&&!isNaN(new Date(e.dateModification).getTime())?e.dateModification:t}});t(i),r(n.categories),W(n)}catch(e){console.error("Error loading products:",e),F("Erreur lors du chargement des produits")}},H=()=>{let t=e;d&&(t=t.filter(e=>e.nom.toLowerCase().includes(d.toLowerCase())||e.codeQR.toLowerCase().includes(d.toLowerCase())||e.description.toLowerCase().includes(d.toLowerCase()))),m&&(t=t.filter(e=>e.categorie===m)),"all"!==p&&(t=t.filter(e=>Ds(e)===p)),l(t)},Y=e=>{switch(e){case"out_of_stock":return"error";case"low_stock":return"warning";case"in_stock":return"success";default:return"default"}},K=e=>{switch(e){case"out_of_stock":return i.jsx(_,{});case"low_stock":return i.jsx($,{});case"in_stock":return i.jsx(z,{});default:return i.jsx(v,{})}},G=e=>{switch(e){case"out_of_stock":return"Rupture";case"low_stock":return"Stock bas";case"in_stock":return"En stock";default:return"Inconnu"}},Z=e=>{e?(C(e),P({nom:e.nom,description:e.description,prixAchatCDF:e.prixAchatCDF||0,prixCDF:e.prixCDF,categorie:e.categorie,stock:e.stock,stockMin:e.stockMin})):(C(null),P({nom:"",description:"",prixAchatCDF:0,prixCDF:0,categorie:"",stock:0,stockMin:0})),D(!0),F(""),R(""),setTimeout(()=>{document.querySelectorAll('div[role="dialog"] input, div[role="dialog"] textarea').forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})},100)},se=()=>{D(!1),C(null),F(""),R("")},re=e.length,ae=e.filter(e=>"in_stock"===Ds(e)).length,oe=e.filter(e=>"low_stock"===Ds(e)).length,le=e.reduce((e,t)=>e+t.prixCDF*t.stock,0);return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Inventaire"}),X.canManageProducts&&i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>Z(),children:"Nouveau Produit"})]}),k&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>R(""),children:k}),E&&i.jsx(O,{severity:"error",sx:{mb:2},action:i.jsx(A,{color:"inherit",size:"small",onClick:()=>{window.confirm("Cela va supprimer toutes les données et réinitialiser l'application. Continuer?")&&(localStorage.clear(),window.location.reload())},children:"Réinitialiser les données"}),children:E}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Produits"}),i.jsx(o,{variant:"h6",children:re})]}),i.jsx(v,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Stock"}),i.jsx(o,{variant:"h6",color:"success.main",children:ae})]}),i.jsx(z,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Stock Bas"}),i.jsx(o,{variant:"h6",color:"warning.main",children:oe})]}),i.jsx($,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Valeur Inventaire"}),i.jsx(o,{variant:"h6",color:"primary",children:Ss(le,"CDF")})]}),i.jsx(v,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom, Code QR ou description...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Catégorie"}),i.jsxs(Se,{value:m,label:"Catégorie",onChange:e=>h(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Statut Stock"}),i.jsxs(Se,{value:p,label:"Statut Stock",onChange:e=>x(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"in_stock",children:"En stock"}),i.jsx(J,{value:"low_stock",children:"Stock bas"}),i.jsx(J,{value:"out_of_stock",children:"Rupture"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:2,children:i.jsxs(s,{display:"flex",gap:1,children:[i.jsx(U,{title:"Exporter CSV",children:i.jsx(T,{onClick:()=>{const e=Ii.arrayToCSV(a,Ai),t=new Blob([e],{type:"text/csv;charset=utf-8"}),n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download=`SmartBoutique_Produits_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),R("Produits exportés en CSV avec succès"),setTimeout(()=>R(""),3e3)},children:i.jsx(Fe,{})})}),i.jsx(U,{title:"Importer CSV",children:i.jsx(T,{onClick:()=>N(!0),children:i.jsx(ke,{})})})]})})]})}),i.jsxs(he,{component:c,children:[i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Produit"}),i.jsx(ye,{children:"Code QR"}),i.jsx(ye,{children:"Catégorie"}),i.jsx(ye,{align:"right",children:"Prix CDF"}),i.jsx(ye,{align:"right",children:"Prix USD"}),i.jsx(ye,{align:"center",children:"Stock"}),i.jsx(ye,{align:"center",children:"Statut"}),i.jsx(ye,{children:"Dernière Modif."}),X.canManageProducts&&i.jsx(ye,{align:"center",children:"Actions"})]})}),i.jsx(je,{children:(-1===j?a:a.slice(g*j,g*j+j)).map(n=>{const r=Ds(n);return i.jsxs(ge,{hover:!0,onClick:()=>Z(n),sx:{cursor:"pointer"},children:[i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),i.jsx(o,{variant:"caption",color:"text.secondary",children:n.description})]})}),i.jsx(ye,{children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[n.codeQR,i.jsx(U,{title:"Code QR",children:i.jsx(T,{size:"small",children:i.jsx(Re,{fontSize:"small"})})})]})}),i.jsx(ye,{children:n.categorie}),i.jsx(ye,{align:"right",children:Ss(n.prixCDF,"CDF")}),i.jsx(ye,{align:"right",children:n.prixUSD?Ss(n.prixUSD,"USD"):"-"}),i.jsx(ye,{align:"center",children:i.jsxs(s,{children:[i.jsx(o,{variant:"body2",children:n.stock}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Min: ",n.stockMin]})]})}),i.jsx(ye,{align:"center",children:i.jsx(I,{icon:K(r),label:G(r),color:Y(r),size:"small"})}),i.jsx(ye,{children:(()=>{try{const e=new Date(n.dateModification);return isNaN(e.getTime())?"Date invalide":Tt(e,"dd/MM/yyyy",{locale:cs})}catch(e){return"Date invalide"}})()}),X.canManageProducts&&i.jsx(ye,{align:"center",children:i.jsxs(s,{display:"flex",gap:1,children:[i.jsx(U,{title:"Modifier",children:i.jsx(T,{size:"small",onClick:()=>Z(n),children:i.jsx(Me,{fontSize:"small"})})}),is.hasRole(["super_admin"])&&i.jsx(U,{title:"Supprimer",children:i.jsx(T,{size:"small",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le produit "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await ns.setProducts(i),R("Produit supprimé avec succès"),setTimeout(()=>R(""),3e3)}})(n),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id)})})]}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:a.length,rowsPerPage:-1===j?a.length:j,page:-1===j?0:g,onPageChange:(e,t)=>{-1!==j&&y(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);b(t),y(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:S,onClose:se,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ne,{children:f?"Modifier le Produit":"Nouveau Produit"}),i.jsxs(Le,{children:[E&&i.jsx(O,{severity:"error",sx:{mb:2},children:E}),k&&i.jsx(O,{severity:"success",sx:{mb:2},children:k}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom du produit *",value:w.nom,onChange:e=>P({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:3,value:w.description,onChange:e=>P({...w,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Es,{label:"Prix d'achat *",value:w.prixAchatCDF,onChange:e=>P({...w,prixAchatCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixAchatCDF<=0,helperText:w.prixAchatCDF<=0?"Le prix d'achat doit être supérieur à zéro":"Prix d'achat du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Es,{label:"Prix de vente *",value:w.prixCDF,onChange:e=>P({...w,prixCDF:e}),min:0,max:1e7,step:100,exchangeRate:B.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0,error:w.prixCDF<=w.prixAchatCDF,helperText:w.prixCDF<=w.prixAchatCDF?"Le prix de vente doit être supérieur au prix d'achat pour générer un bénéfice":"Prix de vente du produit en CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Catégorie *"}),i.jsx(Se,{value:w.categorie,label:"Catégorie *",onChange:e=>P({...w,categorie:e.target.value}),children:n.map(e=>i.jsx(J,{value:e.nom,children:e.nom},e.id))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Us,{value:w.stock,onChange:e=>P({...w,stock:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock actuel",helperText:"Quantité actuelle en stock"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Us,{value:w.stockMin,onChange:e=>P({...w,stockMin:e}),min:0,max:999999,size:"medium",showButtons:!0,allowDirectInput:!0,label:"Stock minimum",helperText:"Seuil d'alerte pour stock bas"})})]})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:se,children:"Annuler"}),i.jsx(A,{onClick:async()=>{if(!w.nom.trim())return void F("Le nom du produit est requis");if(w.prixAchatCDF<=0)return void F("Le prix d'achat doit être supérieur à zéro");if(w.prixCDF<=0)return void F("Le prix de vente doit être supérieur à zéro");if(w.stock<0)return void F("Le stock ne peut pas être négatif");if(w.stockMin<0)return void F("Le stock minimum ne peut pas être négatif");if(!w.categorie)return void F("La catégorie est requise");const n=(i=w.prixAchatCDF,s=w.prixCDF,i<=0?{isValid:!1,errorMessage:"Le prix d'achat doit être supérieur à zéro"}:s<=0?{isValid:!1,errorMessage:"Le prix de vente doit être supérieur à zéro"}:s<=i?{isValid:!1,errorMessage:"Le prix de vente doit être supérieur au prix d'achat"}:{isValid:!0});var i,s;if(!n.isValid)return void F(n.errorMessage||"Erreur de validation des prix");const r=await ns.getSettings(),a=(new Date).toISOString(),o=((e,t,n)=>{const i=t-e;return{beneficeUnitaireCDF:i,beneficeUnitaireUSD:i/n}})(w.prixAchatCDF,w.prixCDF,r.tauxChangeUSDCDF);if(f){const n={...f,nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ps(w.prixAchatCDF,r.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ps(w.prixCDF,r.tauxChangeUSDCDF),beneficeUnitaireCDF:o.beneficeUnitaireCDF,beneficeUnitaireUSD:o.beneficeUnitaireUSD,categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,dateModification:a},i=e.map(e=>e.id===f.id?n:e);t(i),await ns.setProducts(i),R("Produit mis à jour avec succès")}else{const n={id:Date.now().toString(),nom:w.nom.trim(),description:w.description.trim(),prixAchatCDF:w.prixAchatCDF,prixAchatUSD:Ps(w.prixAchatCDF,r.tauxChangeUSDCDF),prixCDF:w.prixCDF,prixUSD:Ps(w.prixCDF,r.tauxChangeUSDCDF),beneficeUnitaireCDF:o.beneficeUnitaireCDF,beneficeUnitaireUSD:o.beneficeUnitaireUSD,codeQR:Cs(),categorie:w.categorie,stock:w.stock,stockMin:w.stockMin,codeBarres:fs(),dateCreation:a,dateModification:a},i=[...e,n];t(i),await ns.setProducts(i),R("Produit créé avec succès")}setTimeout(()=>{se()},1500)},variant:"contained",children:f?"Mettre à jour":"Créer"})]})]}),i.jsxs(Ae,{open:M,onClose:()=>N(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Ne,{children:"Importer des Produits depuis CSV"}),i.jsxs(Le,{children:[i.jsx(Ve,{sx:{mb:2},children:"Collez le contenu CSV des produits à importer. Format attendu: ID, Nom du Produit, Description, Prix CDF, Prix USD, Code QR, Catégorie, Stock, Stock Minimum, Code Barres, Date de Création, Date de Modification"}),i.jsx(ne,{fullWidth:!0,multiline:!0,rows:10,value:L,onChange:e=>V(e.target.value),placeholder:"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\n1,iPhone 15,Smartphone Apple,2240000,800,SB123,Électronique,25,5,1234567890123,2024-01-01,2024-01-01",variant:"outlined"})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:()=>N(!1),children:"Annuler"}),i.jsx(A,{onClick:async()=>{if(L.trim())try{const n=Ii.csvToArray(L,Ai),i=Ii.validateCSVData(n,Ai);if(!i.isValid)return void F("Données CSV invalides: "+i.errors.join(", "));const s=n.map((e,t)=>({...e,id:e.id||Date.now().toString()+t,dateCreation:e.dateCreation||(new Date).toISOString(),dateModification:e.dateModification||(new Date).toISOString(),codeQR:e.codeQR||Cs(),codeBarres:e.codeBarres||fs()})),r=new Set(e.map(e=>e.id)),a=s.filter(e=>!r.has(e.id)),o=[...e,...a];t(o),await ns.setProducts(o),R(`${a.length} produits importés avec succès`),N(!1),V(""),setTimeout(()=>R(""),3e3)}catch(n){F("Erreur lors de l'importation: "+n.message)}else F("Veuillez saisir le contenu CSV à importer")},variant:"contained",children:"Importer"})]})]})]})};class Fs{static async generateSalesReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ns.getSales()).filter(t=>{var n;return new Date(t.datevente).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RV-${e}`))}).length+1).toString().padStart(4,"0");return`RV-${e}-${t}`}static async generateExpenseReceiptNumber(){const e=(new Date).toISOString().slice(0,10).replace(/-/g,""),t=((await ns.getExpenses()).filter(t=>{var n;return new Date(t.dateDepense).toISOString().slice(0,10).replace(/-/g,"")===e&&(null==(n=t.numeroRecu)?void 0:n.startsWith(`RD-${e}`))}).length+1).toString().padStart(4,"0");return`RD-${e}-${t}`}static async createSalesReceiptData(e){const t=await ns.getSettings();return{type:"sale",numero:e.numeroRecu||await this.generateSalesReceiptNumber(),date:e.datevente,entreprise:t.entreprise,sale:e,vendeur:e.vendeur}}static async createExpenseReceiptData(e){const t=await ns.getSettings();return{type:"expense",numero:e.numeroRecu||await this.generateExpenseReceiptNumber(),date:e.dateDepense,entreprise:t.entreprise,expense:e,creePar:e.creePar}}static getPaymentMethodLabel(e){switch(e){case"cash":return"Cash";case"card":return"Carte";case"mobile_money":return"Mobile Money";default:return e}}static formatCurrency(e,t){return"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`}static formatReceiptDate(e){return new Date(e).toLocaleDateString("fr-FR",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit"})}static async printReceipt(){return new Promise(e=>{try{if("undefined"!=typeof window&&window.process&&"renderer"===window.process.type)try{const{ipcRenderer:t}=window.require("electron"),n={silent:!1,printBackground:!0,color:!1,margin:{marginType:"none"},landscape:!1,pagesPerSheet:1,collate:!1,copies:1,header:"",footer:""};t.invoke("print-receipt",n).then(()=>{console.log("Receipt printed successfully via Electron IPC"),e()}).catch(t=>{console.error("Electron IPC print failed:",t),window.print(),e()})}catch(t){console.error("IPC not available, falling back to window.print():",t),window.print(),e()}else window.print(),e()}catch(er){console.error("Error during printing:",er),window.print(),e()}})}static async shouldAutoPrint(){var e;return(null==(e=(await ns.getSettings()).impression)?void 0:e.impressionAutomatique)||!1}static async getPaperSize(){var e;return(null==(e=(await ns.getSettings()).impression)?void 0:e.taillePapier)||"thermal"}}const ks=({receiptData:e,paperSize:t="thermal"})=>{const{sale:n,entreprise:r,numero:a,vendeur:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(s,{textAlign:"center",mb:2,children:[r.logo&&i.jsx(s,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(s,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(s,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE VENTE #",a]}),i.jsx(o,{variant:"body2",children:Fs.formatReceiptDate(n.datevente)})]}),i.jsx(s,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Vendeur:"})," ",l]})}),i.jsxs(s,{mb:2,children:[i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Client:"})," ",n.nomClient]}),n.telephoneClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Téléphone:"})," ",n.telephoneClient]}),n.adresseClient&&i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Adresse:"})," ",n.adresseClient]})]}),i.jsx(h,{sx:{my:1}}),i.jsx(he,{children:i.jsxs(pe,{size:"small",sx:{"& .MuiTableCell-root":{padding:"4px",border:"none"}},children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:i.jsx("strong",{children:"Produit"})}),i.jsx(ye,{align:"center",children:i.jsx("strong",{children:"Qté"})}),i.jsx(ye,{align:"right",children:i.jsx("strong",{children:"Prix unit."})}),i.jsx(ye,{align:"right",children:i.jsx("strong",{children:"Sous-total"})})]})}),i.jsx(je,{children:n.produits.map((e,t)=>i.jsxs(ge,{children:[i.jsx(ye,{sx:{fontSize:"11px"},children:e.nomProduit}),i.jsx(ye,{align:"center",sx:{fontSize:"11px"},children:e.quantite}),i.jsx(ye,{align:"right",sx:{fontSize:"11px"},children:Fs.formatCurrency(e.prixUnitaireCDF,"CDF")}),i.jsx(ye,{align:"right",sx:{fontSize:"11px"},children:Fs.formatCurrency(e.totalCDF,"CDF")})]},t))})]})}),i.jsx(h,{sx:{my:1}}),i.jsx(s,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Méthode de paiement:"})," ",Fs.getPaymentMethodLabel(n.methodePaiement)]})}),i.jsxs(s,{mb:2,children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Sous-total:"}),i.jsx(o,{variant:"body2",children:Fs.formatCurrency(n.totalCDF,"CDF")})]}),i.jsxs(s,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Total CDF:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Fs.formatCurrency(n.totalCDF,"CDF")})]}),n.totalUSD&&i.jsxs(s,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Total USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Fs.formatCurrency(n.totalUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsx(s,{mb:2,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Notes:"})," ",n.notes]})}),i.jsxs(s,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Merci pour votre achat!"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",a]})]})]})},Rs=({receiptData:e,paperSize:t="thermal"})=>{const{expense:n,entreprise:r,numero:a,creePar:l}=e;return i.jsxs(c,{className:"receipt-container "+("thermal"===t?"thermal-receipt":"a4-receipt"),elevation:0,sx:{p:2,fontFamily:"monospace",fontSize:"thermal"===t?"12px":"14px",lineHeight:1.4,maxWidth:"thermal"===t?"80mm":"210mm",margin:"0 auto",backgroundColor:"white",color:"black","@media print":{backgroundColor:"white !important",color:"black !important",boxShadow:"none !important",border:"none !important"}},children:[i.jsxs(s,{textAlign:"center",mb:2,children:[r.logo&&i.jsx(s,{mb:1,display:"flex",justifyContent:"center",alignItems:"center",children:i.jsx(s,{component:"img",src:r.logo,alt:`${r.nom} Logo`,sx:{maxWidth:"thermal"===t?"70mm":"150px",maxHeight:"60px",objectFit:"contain",mb:1}})}),i.jsx(o,{variant:"h6",fontWeight:"bold",sx:{fontSize:"16px"},children:r.nom}),i.jsx(o,{variant:"body2",sx:{fontSize:"12px"},children:r.adresse}),i.jsxs(o,{variant:"body2",sx:{fontSize:"12px"},children:["Tél: ",r.telephone]}),r.rccm&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["RCCM: ",r.rccm]}),r.idNat&&i.jsxs(o,{variant:"body2",sx:{fontSize:"11px"},children:["ID NAT: ",r.idNat]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(s,{textAlign:"center",mb:2,children:[i.jsxs(o,{variant:"h6",fontWeight:"bold",children:["REÇU DE DÉPENSE #",a]}),i.jsx(o,{variant:"body2",children:Fs.formatReceiptDate(n.dateDepense)})]}),i.jsx(s,{mb:1,children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Créé par:"})," ",l]})}),i.jsx(h,{sx:{my:1}}),i.jsxs(s,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Description:"})}),i.jsx(o,{variant:"body2",sx:{pl:1,mb:2},children:n.description}),i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Catégorie:"})," ",n.categorie]})]}),i.jsx(h,{sx:{my:1}}),i.jsxs(s,{mb:2,children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",mb:1,children:[i.jsx(o,{variant:"body1",fontWeight:"bold",children:"Montant:"}),i.jsx(o,{variant:"body1",fontWeight:"bold",children:Fs.formatCurrency(n.montantCDF,"CDF")})]}),n.montantUSD&&i.jsxs(s,{display:"flex",justifyContent:"space-between",children:[i.jsx(o,{variant:"body2",children:"Équivalent USD:"}),i.jsxs(o,{variant:"body2",children:["≈ ",Fs.formatCurrency(n.montantUSD,"USD")]})]})]}),i.jsx(h,{sx:{my:1}}),n.notes&&i.jsxs(s,{mb:2,children:[i.jsx(o,{variant:"body2",mb:1,children:i.jsx("strong",{children:"Notes:"})}),i.jsx(o,{variant:"body2",sx:{pl:1},children:n.notes})]}),i.jsxs(s,{textAlign:"center",mt:2,children:[i.jsx(o,{variant:"body2",fontWeight:"bold",children:"Reçu de dépense validé"}),i.jsxs(o,{variant:"caption",sx:{fontSize:"10px"},children:["Reçu ID: ",a]})]})]})},Ms=({open:e,onClose:t,receiptData:n,paperSize:r="thermal",onPrintSuccess:a})=>{const[l,c]=Dt.useState(!1);return n?i.jsxs(Ae,{open:e,onClose:t,maxWidth:"md",fullWidth:!0,className:"receipt-preview-modal",PaperProps:{sx:{maxHeight:"90vh","@media print":{boxShadow:"none",margin:0,maxWidth:"none",maxHeight:"none"}}},children:[i.jsxs(Ne,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center","@media print":{display:"none"}},children:[i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(ae,{}),i.jsx(o,{variant:"h6",children:"Aperçu du reçu"})]}),i.jsx(T,{onClick:t,size:"small",children:i.jsx(qe,{})})]}),i.jsx(Le,{sx:{p:0,"@media print":{padding:0,margin:0}},children:i.jsx(s,{sx:{p:2,"@media print":{padding:0,margin:0}},children:"sale"===n.type?i.jsx(ks,{receiptData:n,paperSize:r}):i.jsx(Rs,{receiptData:n,paperSize:r})})}),i.jsxs(Oe,{sx:{p:2,gap:1,"@media print":{display:"none"}},children:[i.jsx(A,{onClick:t,variant:"outlined",disabled:l,children:"Fermer"}),i.jsx(A,{onClick:async()=>{if(n){c(!0);try{const e=document.createElement("div");e.className="receipt-print-container",e.style.cssText="\n        position: fixed;\n        top: -9999px;\n        left: -9999px;\n        width: 100%;\n        height: auto;\n        background: white;\n        z-index: 9999;\n      ";const n=document.querySelector(".receipt-container");if(n){const t=n.cloneNode(!0);t.style.cssText="\n          display: block !important;\n          position: static !important;\n          width: 100% !important;\n          height: auto !important;\n          margin: 0 !important;\n          padding: 5mm !important;\n          background: white !important;\n          color: black !important;\n        ",e.appendChild(t)}document.body.appendChild(e),await new Promise(e=>setTimeout(e,200));const i=document.querySelector(".receipt-preview-modal");i&&(i.style.display="none"),await Fs.printReceipt(),document.body.removeChild(e),i&&(i.style.display="block"),a&&a(),setTimeout(()=>{t()},500)}catch(er){console.error("Error printing receipt:",er)}finally{c(!1)}}},variant:"contained",startIcon:l?i.jsx(Be,{size:16}):i.jsx(ze,{}),disabled:l,children:l?"Impression...":"Imprimer"})]})]}):null},Is=()=>{const[e,t]=Dt.useState([]),[n,r]=Dt.useState([]),[a,l]=Dt.useState([]),[d,u]=Dt.useState(""),[m,g]=Dt.useState(0),[j,v]=Dt.useState(10),[D,f]=Dt.useState(!1),[w,P]=Dt.useState(!1),[E,F]=Dt.useState(null),[k,R]=Dt.useState(""),[M,N]=Dt.useState(""),[L,q]=Dt.useState([]),[B,z]=Dt.useState(null),[_,$]=Dt.useState(1),[W,X]=Dt.useState(""),[Q,H]=Dt.useState(""),[Y,K]=Dt.useState(""),[G,Z]=Dt.useState("cash"),[se,re]=Dt.useState("cash"),[ae,oe]=Dt.useState("CDF"),[le,de]=Dt.useState(""),[ue,me]=Dt.useState(""),[De,Ce]=Dt.useState(!1),[we,Pe]=Dt.useState({tauxChangeUSDCDF:2800}),[Ee,Fe]=Dt.useState(!1),[ke,Re]=Dt.useState(null),[Me,Ve]=Dt.useState(!1),qe=is.getUserPermissions(),ze=is.getCurrentUser(),Ke=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};Dt.useEffect(()=>{Ge()},[]),Dt.useEffect(()=>{Ze()},[e,d]),Dt.useEffect(()=>{-1===j&&v(a.length||1)},[a.length,j]);const Ge=async()=>{const e=await ns.getSales(),n=await ns.getProducts(),i=await ns.getSettings();t(e),r(n),Pe(i)},Ze=()=>{let t=e;d&&(t=t.filter(e=>e.nomClient.toLowerCase().includes(d.toLowerCase())||e.id.toLowerCase().includes(d.toLowerCase())||e.vendeur.toLowerCase().includes(d.toLowerCase()))),l(t)},et=()=>{f(!1),tt(),R(""),N("")},tt=()=>{q([]),z(null),$(1),X(""),H(""),K(""),Z("cash"),re("cash"),oe("CDF"),de(""),me(""),Ce(!1)},nt=e=>{const t=L.filter((t,n)=>n!==e);q(t)},it=()=>L.reduce((e,t)=>({CDF:e.CDF+t.totalCDF,USD:e.USD+(t.totalUSD||0)}),{CDF:0,USD:0}),st=e=>"USD"===ae?{price:e.prixUSD||e.prixCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),currency:"USD",symbol:"$"}:{price:e.prixCDF,currency:"CDF",symbol:""},rt=e=>{const t=st(e);return`${t.symbol}${t.price.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===t.currency?2:0,maximumFractionDigits:"USD"===t.currency?2:0})} ${t.currency}`},at=e=>{F(e),P(!0)},ot=e=>{switch(e){case"cash":default:return i.jsx(C,{});case"card":return i.jsx($e,{});case"mobile_money":return i.jsx(Ye,{})}},lt=e=>{switch(e){case"cash":return"Cash";case"card":return"Carte";case"mobile_money":return"Mobile Money";default:return e}},ct=e.filter(e=>{const t=Ke(e.datevente);if(!t)return!1;const n=new Date;return t.toDateString()===n.toDateString()}),dt=ct.reduce((e,t)=>e+t.totalCDF,0),ut=e.length,mt=e.reduce((e,t)=>e+t.totalCDF,0),ht=ut>0?mt/ut:0;return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Ventes"}),qe.canManageSales&&i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>{tt(),f(!0),R(""),N("")},children:"Nouvelle Vente"})]}),M&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>N(""),children:M}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes du jour"}),i.jsx(o,{variant:"h6",children:ct.length}),i.jsx(o,{variant:"body2",color:"primary",children:Ss(dt,"CDF")})]}),i.jsx(b,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Ventes"}),i.jsx(o,{variant:"h6",children:ut}),i.jsx(o,{variant:"body2",color:"success.main",children:Ss(mt,"CDF")})]}),i.jsx(_e,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Vente Moyenne"}),i.jsx(o,{variant:"h6",children:Ss(ht,"CDF")})]}),i.jsx(S,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",children:e.filter(e=>"credit"===e.typeVente).length})]}),i.jsx($e,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, ID vente ou vendeur...",value:d,onChange:e=>u(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsxs(he,{component:c,children:[i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Produits Vendus"}),i.jsx(ye,{children:"Client"}),i.jsx(ye,{children:"Vendeur"}),i.jsx(ye,{align:"right",children:"Total CDF"}),i.jsx(ye,{align:"right",children:"Total USD"}),i.jsx(ye,{align:"center",children:"Paiement"}),i.jsx(ye,{align:"center",children:"Type"}),i.jsx(ye,{children:"Date"}),i.jsx(ye,{align:"center",children:"Actions"})]})}),i.jsx(je,{children:(-1===j?a:a.slice(m*j,m*j+j)).map(e=>i.jsxs(ge,{hover:!0,onClick:()=>at(e),sx:{cursor:"pointer"},children:[i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",color:"primary",gutterBottom:!0,children:e.produits.map(e=>e.nomProduit).join(", ")}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["ID: ",e.id," • ",e.produits.length," article",e.produits.length>1?"s":""]})]})}),i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",children:e.nomClient}),e.telephoneClient&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.telephoneClient})]})}),i.jsx(ye,{children:e.vendeur}),i.jsx(ye,{align:"right",children:Ss(e.totalCDF,"CDF")}),i.jsx(ye,{align:"right",children:e.totalUSD?Ss(e.totalUSD,"USD"):"-"}),i.jsx(ye,{align:"center",children:i.jsx(I,{icon:ot(e.methodePaiement),label:lt(e.methodePaiement),size:"small"})}),i.jsx(ye,{align:"center",children:i.jsx(I,{label:"cash"===e.typeVente?"Cash":"Crédit",color:"cash"===e.typeVente?"success":"warning",size:"small"})}),i.jsx(ye,{children:(()=>{const t=Ke(e.datevente);return t?Tt(t,"dd/MM/yyyy HH:mm",{locale:cs}):"Date invalide"})()}),i.jsx(ye,{align:"center",children:i.jsx(U,{title:"Voir détails",children:i.jsx(T,{size:"small",onClick:()=>at(e),children:i.jsx(C,{fontSize:"small"})})})})]},e.id))})]}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:a.length,rowsPerPage:-1===j?a.length:j,page:-1===j?0:m,onPageChange:(e,t)=>{-1!==j&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);v(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===j?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:D,onClose:et,maxWidth:"lg",fullWidth:!0,children:[i.jsx(Ne,{children:"Nouvelle Vente"}),i.jsxs(Le,{children:[k&&i.jsx(O,{severity:"error",sx:{mb:2},children:k}),M&&i.jsx(O,{severity:"success",sx:{mb:2},children:M}),i.jsxs(s,{sx:{mb:3,p:2,bgcolor:"primary.50",borderRadius:1,border:"1px solid",borderColor:"primary.200"},children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,sx:{fontWeight:"bold",color:"primary.main"},children:"💱 Devise de la Vente"}),i.jsxs(ve,{fullWidth:!0,size:"small",children:[i.jsx(be,{children:"Sélectionnez la devise pour cette vente"}),i.jsxs(Se,{value:ae,label:"Sélectionnez la devise pour cette vente",onChange:e=>oe(e.target.value),children:[i.jsx(J,{value:"CDF",children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇨🇩"}),i.jsx(o,{children:"CDF (Franc Congolais) - Devise principale"})]})}),i.jsx(J,{value:"USD",children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{children:"🇺🇸"}),i.jsx(o,{children:"USD (Dollar Américain)"})]})})]})]}),i.jsx(o,{variant:"caption",color:"text.secondary",sx:{mt:1,display:"block"},children:"Les prix des produits seront affichés dans la devise sélectionnée"})]}),i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Sélection des Produits"}),i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(We,{options:n.filter(e=>e.stock>0),getOptionLabel:e=>`${e.nom} - ${rt(e)} - Stock: ${e.stock}`,value:B,onChange:(e,t)=>z(t),renderInput:e=>i.jsx(ne,{...e,label:"Produit",fullWidth:!0}),renderOption:(e,t)=>i.jsx(s,{component:"li",...e,children:i.jsxs(s,{sx:{display:"flex",flexDirection:"column",width:"100%"},children:[i.jsx(o,{variant:"body1",sx:{fontWeight:500},children:t.nom}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Prix: ",rt(t)," • Stock: ",t.stock," • Code: ",t.codeQR]})]})})})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(s,{children:[i.jsx(Us,{value:_,onChange:$,min:1,max:(null==B?void 0:B.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0,label:"Quantité"}),B&&i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",sx:{mt:1},children:["Stock disponible: ",B.stock]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(A,{fullWidth:!0,variant:"contained",onClick:()=>{if(!B)return void R("Veuillez sélectionner un produit");if(_<=0)return void R("La quantité doit être supérieure à 0");if(_>B.stock)return void R("Quantité insuffisante en stock");const e=L.findIndex(e=>e.produitId===B.id);if(e>=0){const t=[...L],n=t[e].quantite+_;if(n>B.stock)return void R("Quantité totale dépasse le stock disponible");t[e]={...t[e],quantite:n,totalCDF:n*B.prixCDF,totalUSD:B.prixUSD?n*B.prixUSD:void 0},q(t)}else{const e={produitId:B.id,nomProduit:B.nom,quantite:_,prixUnitaireCDF:B.prixCDF,prixUnitaireUSD:B.prixUSD,totalCDF:_*B.prixCDF,totalUSD:B.prixUSD?_*B.prixUSD:void 0};q([...L,e])}z(null),$(1),R("")},disabled:!B,children:"Ajouter"})})]}),B&&i.jsx(s,{sx:{mt:2,p:2,bgcolor:"grey.50",borderRadius:1,border:"1px solid",borderColor:"grey.300"},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Produit sélectionné: ",B.nom]}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Code: ",B.codeQR," • Catégorie: ",B.categorie]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",color:"primary",sx:{fontWeight:"bold"},children:["Prix unitaire: ",rt(B)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Devise: ",ae]})]}),i.jsxs(ce,{item:!0,xs:12,md:3,children:[i.jsxs(o,{variant:"subtitle2",sx:{fontWeight:"bold"},children:["Total: ",(()=>{const e=st(B),t=e.price*_;return`${e.symbol}${t.toLocaleString("fr-FR",{minimumFractionDigits:"USD"===e.currency?2:0,maximumFractionDigits:"USD"===e.currency?2:0})} ${e.currency}`})()]}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[_," × ",rt(B)]})]})]})})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:["Panier (",L.length," articles) - Devise: ",ae]}),0===L.length?i.jsx(O,{severity:"info",children:"Aucun produit ajouté"}):i.jsxs(p,{children:[L.map((e,t)=>{var r;const a=(e=>{if("USD"===ae){const t=e.prixUnitaireUSD||e.prixUnitaireCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800),n=e.totalUSD||e.totalCDF/((null==we?void 0:we.tauxChangeUSDCDF)||2800);return{unitPrice:`$${t.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`,total:`$${n.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`}}return{unitPrice:`${e.prixUnitaireCDF.toLocaleString("fr-FR")} CDF`,total:`${e.totalCDF.toLocaleString("fr-FR")} CDF`}})(e);return i.jsxs(Ct.Fragment,{children:[i.jsxs(x,{children:[i.jsx(y,{primary:i.jsx(o,{variant:"subtitle1",sx:{fontWeight:500},children:e.nomProduit}),secondary:`Prix unitaire: ${a.unitPrice}`}),i.jsx(V,{children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(Us,{value:e.quantite,onChange:e=>((e,t)=>{if(t<=0)return void nt(e);const i=L[e],s=n.find(e=>e.id===i.produitId);if(s&&t>s.stock)return void R(`Quantité maximale disponible: ${s.stock}`);const r=[...L];r[e]={...i,quantite:t,totalCDF:t*i.prixUnitaireCDF,totalUSD:i.prixUnitaireUSD?t*i.prixUnitaireUSD:void 0},q(r),R("")})(t,e),min:1,max:(null==(r=n.find(t=>t.id===e.produitId))?void 0:r.stock)||999,size:"small",showButtons:!0,allowDirectInput:!0}),i.jsx(o,{variant:"subtitle1",sx:{minWidth:"120px",textAlign:"right",fontWeight:"bold",color:"primary.main"},children:a.total}),i.jsx(T,{size:"small",color:"error",onClick:()=>nt(t),title:"Supprimer l'article",children:i.jsx(_e,{fontSize:"small"})})]})})]}),t<L.length-1&&i.jsx(h,{})]},t)}),i.jsx(h,{sx:{my:2}}),i.jsx(x,{sx:{bgcolor:"primary.50",borderRadius:1},children:i.jsx(y,{primary:i.jsxs(o,{variant:"h6",sx:{fontWeight:"bold",color:"primary.main"},children:["Total: ",(()=>{const e=it();return"USD"===ae?`$${e.USD.toLocaleString("fr-FR",{minimumFractionDigits:2,maximumFractionDigits:2})} USD`:`${e.CDF.toLocaleString("fr-FR")} CDF`})()]}),secondary:i.jsx(o,{variant:"body2",color:"text.secondary",children:"USD"===ae?`≈ ${Ss(it().CDF,"CDF")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`:`≈ ${Ss(it().USD,"USD")} (taux: ${(null==we?void 0:we.tauxChangeUSDCDF)||2800})`})})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations Client"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Nom du client",placeholder:"Client (par défaut)",value:W,onChange:e=>X(e.target.value)})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:Q,onChange:e=>H(e.target.value)})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:Y,onChange:e=>K(e.target.value)})})]})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Informations de Paiement"}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ve,{component:"fieldset",children:[i.jsx(fe,{component:"legend",children:"Type de vente"}),i.jsxs(Xe,{row:!0,value:se,onChange:e=>re(e.target.value),children:[i.jsx(Qe,{value:"cash",control:i.jsx(Je,{}),label:"Cash"}),i.jsx(Qe,{value:"credit",control:i.jsx(Je,{}),label:"Crédit"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Méthode de paiement"}),i.jsxs(Se,{value:G,label:"Méthode de paiement",onChange:e=>Z(e.target.value),children:[i.jsx(J,{value:"cash",children:"Cash"}),i.jsx(J,{value:"card",children:"Carte"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),"credit"===se&&i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date d'échéance",type:"date",value:le,onChange:e=>de(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:ue,onChange:e=>me(e.target.value)})})]})]})]})]}),i.jsxs(Oe,{children:[i.jsx(Qe,{control:i.jsx(He,{checked:De,onChange:e=>Ce(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),Me&&i.jsxs(s,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(Be,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(A,{onClick:et,children:"Annuler"}),i.jsx(A,{onClick:async()=>{var i;if(0===L.length)return void R("Veuillez ajouter au moins un produit");if("credit"===se&&!le)return void R("La date d'échéance est requise pour les ventes à crédit");const s=it(),a=(new Date).toISOString(),o=De||(null==(i=null==we?void 0:we.impression)?void 0:i.impressionAutomatique)||!1,l=o?await Fs.generateSalesReceiptNumber():void 0,c={id:`VTE-${Date.now()}`,produits:L,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:Y.trim()||void 0,totalCDF:s.CDF,totalUSD:s.USD,methodePaiement:G,typeVente:se,datevente:a,vendeur:(null==ze?void 0:ze.nom)||"Inconnu",notes:ue.trim()||void 0,numeroRecu:l},d=n.map(e=>{const t=L.find(t=>t.produitId===e.id);return t?{...e,stock:e.stock-t.quantite,dateModification:a}:e}),u=[...e,c];if(t(u),r(d),await ns.setSales(u),await ns.setProducts(d),"credit"===se){const e={id:`DET-${Date.now()}`,venteId:c.id,nomClient:W.trim()||"Client",telephoneClient:Q.trim()||void 0,adresseClient:Y.trim()||void 0,montantTotalCDF:s.CDF,montantTotalUSD:s.USD,montantPayeCDF:0,montantPayeUSD:0,montantRestantCDF:s.CDF,montantRestantUSD:s.USD,dateCreation:a,dateEcheance:le,statut:"active",statutPaiement:"impaye",paiements:[],notes:ue.trim()||void 0,deviseVente:ae},t=await ns.getDebts();await ns.setDebts([...t,e])}if(N("Vente enregistrée avec succès"),o)try{Ve(!0);const e=await Fs.createSalesReceiptData(c);Re(e),Fe(!0)}catch(m){console.error("Erreur lors de la génération du reçu:",m),R("Erreur lors de la génération du reçu")}finally{Ve(!1)}setTimeout(()=>{et()},1500)},variant:"contained",disabled:0===L.length||Me,children:"Enregistrer la Vente"})]})]}),i.jsxs(Ae,{open:w,onClose:()=>P(!1),maxWidth:"md",fullWidth:!0,children:[i.jsx(Ne,{children:"Détails de la Vente"}),i.jsx(Le,{children:E&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Vendus:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:E.produits.map(e=>e.nomProduit).join(", ")})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:E.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date:"}),i.jsx(o,{variant:"body1",children:Tt(new Date(E.datevente),"dd/MM/yyyy HH:mm",{locale:cs})})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",children:E.nomClient}),E.telephoneClient&&i.jsx(o,{variant:"body2",color:"text.secondary",children:E.telephoneClient})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Vendeur:"}),i.jsx(o,{variant:"body1",children:E.vendeur})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Produits:"}),i.jsxs(p,{dense:!0,children:[E.produits.map((e,t)=>i.jsxs(x,{children:[i.jsx(y,{primary:e.nomProduit,secondary:`${e.quantite} × ${Ss(e.prixUnitaireCDF,"CDF")}`}),i.jsx(o,{variant:"body2",children:Ss(e.totalCDF,"CDF")})]},t)),i.jsx(h,{}),i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(o,{variant:"h6",children:["Total: ",Ss(E.totalCDF,"CDF")]})})})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Méthode de paiement:"}),i.jsx(I,{icon:ot(E.methodePaiement),label:lt(E.methodePaiement)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Type de vente:"}),i.jsx(I,{label:"cash"===E.typeVente?"Cash":"Crédit",color:"cash"===E.typeVente?"success":"warning"})]}),E.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:E.notes})]})]})}),i.jsx(Oe,{children:i.jsx(A,{onClick:()=>P(!1),children:"Fermer"})})]}),i.jsx(Ms,{open:Ee,onClose:()=>Fe(!1),receiptData:ke,onPrintSuccess:()=>{N("Reçu imprimé avec succès"),setTimeout(()=>N(""),3e3)}})]})},As=()=>{const[e,t]=Dt.useState([]),[n,r]=Dt.useState([]),[a,l]=Dt.useState(""),[d,u]=Dt.useState("all"),[m,g]=Dt.useState("all"),[j,v]=Dt.useState(0),[b,D]=Dt.useState(10),[f,w]=Dt.useState(!1),[P,E]=Dt.useState(!1),[F,k]=Dt.useState(null),[R,M]=Dt.useState(0),[N,L]=Dt.useState("cash"),[V,q]=Dt.useState(""),[B,_]=Dt.useState(""),[W,X]=Dt.useState(""),[Q,H]=Dt.useState({tauxChangeUSDCDF:2800}),[K,G]=Dt.useState([]),[Z,se]=Dt.useState(!1),re=is.getUserPermissions(),oe=e=>K.find(t=>t.id===e.venteId),de=e=>{const t=oe(e);return t&&t.produits&&0!==t.produits.length?t.produits.map(e=>`${e.nomProduit} (x${e.quantite})`).join(", "):"Produits non disponibles"},ue=e=>{if(e.nomClient&&""!==e.nomClient.trim()&&"Client"!==e.nomClient)return e.nomClient;const t=oe(e);return t&&t.nomClient&&""!==t.nomClient.trim()&&"Client"!==t.nomClient?t.nomClient:"Client"},me=e=>{try{const t=new Date(e);return isNaN(t.getTime())?(console.warn("Invalid date found:",e),null):t}catch(t){return console.warn("Error parsing date:",e,t),null}};Dt.useEffect(()=>{De();(async()=>{0===(await ns.getDebts()).length&&(console.log("No debts found, forcing initialization..."),await ns.forceInitializeDebts(),setTimeout(()=>De(),500))})()},[]),Dt.useEffect(()=>{fe()},[e,a,d,m]),Dt.useEffect(()=>{-1===b&&D(n.length||1)},[n.length,b]);const De=async()=>{try{const e=await ns.getDebts(),n=await ns.getSettings(),i=await ns.getSales(),s=e.filter(e=>e.id&&e.venteId?(e.nomClient&&""!==e.nomClient.trim()||(e.nomClient="Client"),e.montantTotalCDF=void 0!==e.montantTotalCDF&&null!==e.montantTotalCDF&&""!==e.montantTotalCDF?Number(e.montantTotalCDF):0,e.montantPayeCDF=void 0!==e.montantPayeCDF&&null!==e.montantPayeCDF&&""!==e.montantPayeCDF?Number(e.montantPayeCDF):0,e.montantRestantCDF=void 0!==e.montantRestantCDF&&null!==e.montantRestantCDF&&""!==e.montantRestantCDF?Number(e.montantRestantCDF):e.montantTotalCDF-e.montantPayeCDF,!0):(console.warn("Invalid debt record missing required fields:",e),!1)).map(e=>{try{const t=ze(e);if("paid"===t.statut)return t;const n=new Date,i=me(t.dateEcheance);return t.montantRestantCDF<=0?{...t,statut:"paid",statutPaiement:"paye"}:i&&Mt(n,i)?{...t,statut:"overdue"}:{...t,statut:"active"}}catch(t){return console.warn("Error processing debt status:",e,t),e}});t(s),H(n),G(i),await ns.setDebts(s)}catch(e){console.error("Error loading debt data:",e),_("Erreur lors du chargement des données de dette.")}},fe=()=>{try{let t=e;if(a&&a.trim()){const e=a.toLowerCase().trim();t=t.filter(t=>{var n,i,s,r;try{const a=(null==(n=t.nomClient)?void 0:n.toLowerCase())||"",o=(null==(i=t.id)?void 0:i.toLowerCase())||"",l=(null==(s=t.venteId)?void 0:s.toLowerCase())||"",c=(null==(r=t.telephoneClient)?void 0:r.toLowerCase())||"",d=de(t).toLowerCase();return a.includes(e)||o.includes(e)||l.includes(e)||c.includes(e)||d.includes(e)}catch(a){return console.warn("Error filtering debt:",t,a),!1}})}"all"!==d&&(t=t.filter(e=>e.statut===d)),"all"!==m&&(t=t.filter(e=>e.statutPaiement===m)),r(t)}catch(t){console.error("Error in filterDebts:",t),r(e),_("Erreur lors de la recherche. Affichage de toutes les dettes.")}},Ce=e=>{switch(e){case"active":return"primary";case"overdue":return"error";case"paid":return"success";default:return"default"}},we=e=>{switch(e){case"active":return"Active";case"overdue":return"En retard";case"paid":return"Payée";default:return e}},Pe=e=>{switch(e){case"active":return i.jsx(Ke,{});case"overdue":return i.jsx($,{});case"paid":return i.jsx(z,{});default:return i.jsx(S,{})}},Ee=e=>{switch(e){case"paye":return"Payé";case"impaye":return"Impayé";default:return e}},Ue=e=>{switch(e){case"paye":return i.jsx(Ge,{});case"impaye":return i.jsx(tt,{});default:return i.jsx(S,{})}},ke=()=>{w(!1),k(null),_(""),X("")},Re=e=>{k(e),E(!0)},Me=()=>{E(!1),k(null),se(!1),M(0),q(""),_(""),X("")},Ve=async()=>{if(!F)return;if(R<=0)return void _("Le montant doit être supérieur à 0");if(R>F.montantRestantCDF)return void _("Le montant ne peut pas dépasser le montant restant");const n=(new Date).toISOString(),i={id:`PAY-${Date.now()}`,montantCDF:R,montantUSD:R/Q.tauxChangeUSDCDF,methodePaiement:N,datePaiement:n,notes:V.trim()||void 0},s={...F,montantPayeCDF:F.montantPayeCDF+R,montantPayeUSD:(F.montantPayeUSD||0)+R/Q.tauxChangeUSDCDF,paiements:[...F.paiements,i]},r=ze(s),a={...r,statut:r.montantRestantCDF<=0?"paid":F.statut,statutPaiement:We(r)},o=e.map(e=>e.id===F.id?a:e);t(o),await ns.setDebts(o),k(a),X("Paiement enregistré avec succès"),Z?setTimeout(()=>{se(!1),M(0),q(""),_(""),X("")},2e3):setTimeout(()=>{ke()},1500)},qe=e=>{switch(e){case"cash":default:return i.jsx(C,{});case"banque":return i.jsx($e,{});case"mobile_money":return i.jsx(Ye,{})}},Be=e=>{switch(e){case"cash":return"Cash";case"banque":return"Banque";case"mobile_money":return"Mobile Money";default:return e}},ze=e=>{const t=e.paiements.reduce((e,t)=>e+t.montantCDF,0),n=e.paiements.reduce((e,t)=>e+(t.montantUSD||0),0),i=e.paiements.length>0?t:e.montantPayeCDF,s=e.paiements.length>0?n:e.montantPayeUSD||0,r=e.montantTotalCDF-i,a=e.montantTotalUSD?e.montantTotalUSD-s:void 0,o=e.montantTotalUSD||e.montantTotalCDF/Q.tauxChangeUSDCDF,l=s||i/Q.tauxChangeUSDCDF,c=void 0!==a?Math.max(0,a):Math.max(0,r/Q.tauxChangeUSDCDF);return{...e,montantTotalCDF:e.montantTotalCDF,montantTotalUSD:o,montantPayeCDF:i,montantPayeUSD:l,montantRestantCDF:Math.max(0,r),montantRestantUSD:c}},_e=e=>0===e.montantTotalCDF?100:Math.min(100,e.montantPayeCDF/e.montantTotalCDF*100),We=e=>e.montantPayeCDF>=e.montantTotalCDF?"paye":"impaye",Xe=e=>"paye"===We(e)?"Payé":"Impayé",Je=e=>"paye"===We(e)?"success":"error",He=e=>"paye"===We(e)?i.jsx(z,{fontSize:"small"}):i.jsx(et,{fontSize:"small"}),nt=e.filter(e=>"active"===e.statut),it=e.filter(e=>"overdue"===e.statut),st=e.filter(e=>"paye"===e.statutPaiement),rt=e.filter(e=>"impaye"===e.statutPaiement),at=e.reduce((e,t)=>e+t.montantTotalCDF,0),ot=e.reduce((e,t)=>e+t.montantRestantCDF,0);return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dettes"}),i.jsx(A,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:()=>{const e=["Client,Téléphone,Montant Dû (CDF),Montant Payé (CDF),Montant Restant (CDF),Montant Dû (USD),Montant Payé (USD),Montant Restant (USD),Date Création,Date Échéance,Statut,Statut Paiement,Produits Achetés,Notes",...n.map(e=>{var t,n,i;const s=de(e);return[e.nomClient||`Vente ${e.venteId}`,e.telephoneClient||"",e.montantTotalCDF.toLocaleString("fr-FR"),e.montantPayeCDF.toLocaleString("fr-FR"),e.montantRestantCDF.toLocaleString("fr-FR"),(null==(t=e.montantTotalUSD)?void 0:t.toFixed(2))||"",(null==(n=e.montantPayeUSD)?void 0:n.toFixed(2))||"",(null==(i=e.montantRestantUSD)?void 0:i.toFixed(2))||"",Tt(new Date(e.dateCreation),"dd/MM/yyyy",{locale:cs}),Tt(new Date(e.dateEcheance),"dd/MM/yyyy",{locale:cs}),"active"===e.statut?"Actif":"overdue"===e.statut?"En retard":"Payé","paye"===e.statutPaiement?"Payé":"Impayé",`"${s}"`,`"${e.notes||""}"`].join(",")})].join("\n"),t=new Blob([e],{type:"text/csv;charset=utf-8"}),i=URL.createObjectURL(t),s=document.createElement("a");s.href=i,s.download=`SmartBoutique_Dettes_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),X("Dettes exportées en CSV avec succès"),setTimeout(()=>X(""),3e3)},children:"Exporter les Dettes"})]}),W&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>X(""),children:W}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dettes Actives"}),i.jsx(o,{variant:"h6",children:nt.length}),i.jsx(o,{variant:"body2",color:"primary",children:Ss(nt.reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]}),i.jsx(Ke,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"En Retard"}),i.jsx(o,{variant:"h6",color:"error.main",children:it.length}),i.jsx(o,{variant:"body2",color:"error",children:Ss(it.reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]}),i.jsx($,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Statut Payé"}),i.jsx(o,{variant:"h6",color:"success.main",children:st.length}),i.jsxs(o,{variant:"body2",color:"error",children:["Impayé: ",rt.length]})]}),i.jsx(Ge,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Restant"}),i.jsx(o,{variant:"h6",children:Ss(ot,"CDF")}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["sur ",Ss(at,"CDF")]})]}),i.jsx(S,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par client, produit, ID dette ou ID vente...",value:a,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Statut"}),i.jsxs(Se,{value:d,label:"Statut",onChange:e=>u(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"active",children:"Actives"}),i.jsx(J,{value:"overdue",children:"En retard"}),i.jsx(J,{value:"paid",children:"Payées"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Statut Paiement"}),i.jsxs(Se,{value:m,label:"Statut Paiement",onChange:e=>g(e.target.value),children:[i.jsx(J,{value:"all",children:"Tous"}),i.jsx(J,{value:"paye",children:"Payé"}),i.jsx(J,{value:"impaye",children:"Impayé"})]})]})})]})}),i.jsxs(he,{component:c,children:[i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Client"}),i.jsx(ye,{children:"Montant Dû"}),i.jsx(ye,{align:"right",children:"Payé"}),i.jsx(ye,{align:"right",children:"Restant"}),i.jsx(ye,{align:"center",children:"Progression"}),i.jsx(ye,{align:"center",children:"Statut"}),i.jsx(ye,{align:"center",children:"Statut Paiement"}),i.jsx(ye,{children:"Échéance"}),re.canManageDebts&&i.jsx(ye,{align:"center",children:"Actions"})]})}),i.jsx(je,{children:(-1===b?n:n.slice(j*b,j*b+b)).map(e=>{try{const t=_e(e),n="overdue"===e.statut;return i.jsxs(ge,{hover:!0,onClick:()=>Re(e),sx:{cursor:"pointer"},children:[i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"h6",fontWeight:"bold",color:"primary",gutterBottom:!0,children:ue(e)}),e.telephoneClient&&i.jsxs(o,{variant:"body2",color:"text.secondary",display:"block",sx:{mb:.5},children:["📞 ",e.telephoneClient]}),i.jsxs(o,{variant:"body2",color:"primary",fontWeight:"medium",display:"block",sx:{mb:.5},children:["🛍️ Produits: ",de(e)]}),i.jsxs(o,{variant:"caption",color:"text.secondary",display:"block",children:["Créé le: ",(()=>{try{const t=me(e.dateCreation);return t?Tt(t,"dd/MM/yyyy",{locale:cs}):"Date invalide"}catch(t){return console.warn("Error formatting creation date:",e.dateCreation,t),"Date invalide"}})()," • ID: ",e.venteId||"N/A"]})]})}),i.jsx(ye,{align:"right",children:Ss(e.montantTotalCDF,"CDF")}),i.jsx(ye,{align:"right",children:Ss(e.montantPayeCDF,"CDF")}),i.jsx(ye,{align:"right",children:i.jsx(o,{variant:"body2",color:e.montantRestantCDF>0?"error":"success.main",children:Ss(e.montantRestantCDF,"CDF")})}),i.jsx(ye,{align:"center",children:i.jsxs(s,{sx:{width:100},children:[i.jsx(le,{variant:"determinate",value:t,color:100===t?"success":n?"error":"primary"}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(t),"%"]})]})}),i.jsx(ye,{align:"center",children:i.jsx(I,{icon:Pe(e.statut),label:we(e.statut),color:Ce(e.statut),size:"small"})}),i.jsx(ye,{align:"center",children:i.jsx(I,{icon:He(e),label:Xe(e),color:Je(e),size:"small"})}),i.jsx(ye,{children:i.jsx(o,{variant:"body2",color:n?"error":"text.primary",children:(()=>{const t=me(e.dateEcheance);return t?Tt(t,"dd/MM/yyyy",{locale:cs}):"Date invalide"})()})}),re.canManageDebts&&i.jsx(ye,{align:"center",children:i.jsxs(s,{display:"flex",gap:1,children:[i.jsx(U,{title:"Voir détails",children:i.jsx(T,{size:"small",onClick:()=>Re(e),children:i.jsx(ae,{fontSize:"small"})})}),"paid"!==e.statut&&i.jsx(U,{title:"Ajouter paiement",children:i.jsx(T,{size:"small",color:"primary",onClick:()=>(e=>{k(e),M(e.montantRestantCDF),L("cash"),q(""),w(!0),_(""),X("")})(e),children:i.jsx(Y,{fontSize:"small"})})})]})})]},e.id)}catch(t){return console.error("Error rendering debt row:",e,t),i.jsx(ge,{children:i.jsx(ye,{colSpan:re.canManageDebts?9:8,children:i.jsxs(o,{color:"error",variant:"body2",children:["Erreur d'affichage pour cette dette. ID: ",e.id||"Inconnu"]})})},e.id||`error-${Math.random()}`)}})})]}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===b?n.length:b,page:-1===b?0:j,onPageChange:(e,t)=>{-1!==b&&v(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);D(t),v(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===b?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:f,onClose:ke,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ne,{children:"Ajouter un Paiement"}),i.jsxs(Le,{children:[B&&i.jsx(O,{severity:"error",sx:{mb:2},children:B}),W&&i.jsx(O,{severity:"success",sx:{mb:2},children:W}),F&&i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Client: ",ue(F)]}),i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Ss(F.montantRestantCDF,"CDF")]})]}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Es,{label:"Montant du paiement",value:R,onChange:e=>M(e),min:0,max:F.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Méthode de paiement"}),i.jsxs(Se,{value:N,label:"Méthode de paiement",onChange:e=>L(e.target.value),children:[i.jsx(J,{value:"cash",children:"Espèces"}),i.jsx(J,{value:"banque",children:"Banque"}),i.jsx(J,{value:"mobile_money",children:"Mobile Money"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:2,value:V,onChange:e=>q(e.target.value)})})]})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:ke,children:"Annuler"}),i.jsx(A,{onClick:Ve,variant:"contained",disabled:!F||R<=0,children:"Enregistrer le Paiement"})]})]}),i.jsxs(Ae,{open:P,onClose:Me,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ne,{children:"Détails de la Dette"}),i.jsx(Le,{children:F&&i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",sx:{fontSize:"1.1rem",fontWeight:"bold"},children:"Produits Achetés à Crédit:"}),i.jsx(o,{variant:"h6",color:"primary",sx:{mt:.5,mb:1},children:de(F)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"ID Dette:"}),i.jsx(o,{variant:"body1",children:F.id})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Référence Vente:"}),i.jsx(o,{variant:"body2",color:"text.secondary",children:F.venteId})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Client:"}),i.jsx(o,{variant:"body1",sx:{fontWeight:"medium"},children:ue(F)}),F.telephoneClient&&i.jsxs(s,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Téléphone:"}),i.jsx(o,{variant:"body2",color:"primary",children:F.telephoneClient})]}),F.adresseClient&&i.jsxs(s,{sx:{mt:.5},children:[i.jsx(o,{variant:"caption",color:"text.secondary",children:"Adresse:"}),i.jsx(o,{variant:"body2",color:"text.primary",children:F.adresseClient})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut:"}),i.jsx(I,{icon:Pe(F.statut),label:we(F.statut),color:Ce(F.statut)})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Statut Paiement:"}),i.jsx(s,{display:"flex",alignItems:"center",gap:1,sx:{mt:.5},children:re.canManageDebts?i.jsx(Qe,{control:i.jsx(Ze,{checked:"paye"===F.statutPaiement,onChange:n=>(async(n,i)=>{if(!re.canManageDebts)return;if(!window.confirm(`Êtes-vous sûr de vouloir changer le statut de paiement à "${Ee(i)}" ?\n\nCette action modifiera le statut de paiement de la dette.`))return;const s=e.map(e=>e.id===n?{...e,statutPaiement:i}:e);t(s),await ns.setDebts(s),F&&F.id===n&&k({...F,statutPaiement:i}),X(`Statut de paiement mis à jour: ${Ee(i)}`),setTimeout(()=>{X("")},3e3)})(F.id,n.target.checked?"paye":"impaye"),color:"success",size:"small"}),label:i.jsxs(s,{display:"flex",alignItems:"center",gap:.5,children:[Ue(F.statutPaiement),i.jsx(o,{variant:"body2",children:Ee(F.statutPaiement)})]}),labelPlacement:"end"}):i.jsx(I,{icon:Ue(F.statutPaiement),label:Ee(F.statutPaiement),color:(e=>{switch(e){case"paye":return"success";case"impaye":return"error";default:return"default"}})(F.statutPaiement)})})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Dû:"}),i.jsx(o,{variant:"body1",color:"primary",sx:{fontWeight:"bold"},children:Ss(F.montantTotalCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Payé:"}),i.jsx(o,{variant:"body1",color:"success.main",children:Ss(F.montantPayeCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,md:4,children:[i.jsx(o,{variant:"subtitle2",children:"Montant Restant:"}),i.jsx(o,{variant:"body1",color:"error",children:Ss(F.montantRestantCDF,"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Progression du Paiement:"}),i.jsxs(s,{sx:{width:"100%",mb:1},children:[i.jsx(le,{variant:"determinate",value:_e(F),color:100===_e(F)?"success":"overdue"===F.statut?"error":"primary",sx:{height:10,borderRadius:5}}),i.jsxs(s,{sx:{display:"flex",justifyContent:"space-between",mt:1},children:[i.jsxs(o,{variant:"caption",color:"text.secondary",children:[Math.round(_e(F)),"% payé"]}),i.jsx(o,{variant:"caption",color:"text.secondary",children:F.montantRestantCDF>0?`${Ss(F.montantRestantCDF,"CDF")} restant`:"Entièrement payé"})]})]})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date de Création:"}),i.jsx(o,{variant:"body1",children:(()=>{const e=me(F.dateCreation);return e?Tt(e,"dd/MM/yyyy",{locale:cs}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsx(o,{variant:"subtitle2",children:"Date d'Échéance:"}),i.jsx(o,{variant:"body1",color:"overdue"===F.statut?"error":"text.primary",children:(()=>{const e=me(F.dateEcheance);return e?Tt(e,"dd/MM/yyyy",{locale:cs}):"Date invalide"})()})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,children:["Historique des Paiements (",F.paiements.length,")"]}),0===F.paiements.length?i.jsx(O,{severity:"info",children:"Aucun paiement enregistré"}):i.jsx(p,{dense:!0,children:F.paiements.map((e,t)=>i.jsxs(Ct.Fragment,{children:[i.jsx(x,{children:i.jsx(y,{primary:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[qe(e.methodePaiement),i.jsx(o,{variant:"body2",children:Ss(e.montantCDF,"CDF")}),i.jsx(I,{label:Be(e.methodePaiement),size:"small"})]}),secondary:i.jsxs(s,{children:[i.jsx(o,{variant:"caption",children:(()=>{const t=me(e.datePaiement);return t?Tt(t,"dd/MM/yyyy HH:mm",{locale:cs}):"Date invalide"})()}),e.notes&&i.jsx(o,{variant:"caption",display:"block",children:e.notes})]})})}),t<F.paiements.length-1&&i.jsx(h,{})]},e.id))})]}),re.canManageDebts&&"paid"!==F.statut&&Z&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(c,{elevation:2,sx:{p:2,mt:2,bgcolor:"background.default"},children:[i.jsxs(o,{variant:"subtitle2",gutterBottom:!0,sx:{display:"flex",alignItems:"center",gap:1},children:[i.jsx(Y,{}),"Ajouter un Paiement"]}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsxs(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:["Montant restant: ",Ss(F.montantRestantCDF,"CDF")]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(Es,{label:"Montant à payer",value:R,onChange:e=>M(e),min:0,max:F.montantRestantCDF,step:50,exchangeRate:Q.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Méthode de paiement"}),i.jsxs(Se,{value:N,onChange:e=>L(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"cash",children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(C,{fontSize:"small"}),"Cash"]})}),i.jsx(J,{value:"mobile_money",children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(Ye,{fontSize:"small"}),"Mobile Money"]})}),i.jsx(J,{value:"banque",children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx($e,{fontSize:"small"}),"Banque"]})})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes (optionnel)",value:V,onChange:e=>q(e.target.value),multiline:!0,rows:2,placeholder:"Ajouter des notes sur ce paiement..."})}),B&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"error",children:B})}),W&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(O,{severity:"success",children:W})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(s,{display:"flex",gap:1,justifyContent:"flex-end",children:[i.jsx(A,{variant:"outlined",onClick:()=>{se(!1),M(0),q(""),_(""),X("")},children:"Annuler"}),i.jsx(A,{variant:"contained",color:"success",onClick:()=>{M(F.montantRestantCDF),q("Paiement complet")},startIcon:i.jsx(Y,{}),children:"Paiement Complet"}),i.jsx(A,{variant:"contained",color:"primary",onClick:Ve,startIcon:i.jsx(Y,{}),disabled:R<=0,children:"Payer"})]})})]})]})}),F.notes&&i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"subtitle2",children:"Notes:"}),i.jsx(o,{variant:"body1",children:F.notes})]})]})}),i.jsxs(Oe,{children:[re.canManageDebts&&F&&"paid"!==F.statut&&!Z&&i.jsx(A,{variant:"contained",color:"primary",onClick:()=>{se(!0),M(0),L("cash"),q(""),_(""),X("")},startIcon:i.jsx(Y,{}),sx:{mr:1},children:"Ajouter Paiement"}),i.jsx(A,{onClick:Me,children:"Fermer"})]})]})]})},Ns=(e,t="dd/MM/yyyy")=>{try{const n="string"==typeof e?new Date(e):e;return Lt(n)?Tt(n,t,{locale:cs}):"Date invalide"}catch(er){return console.warn("Invalid date value:",e,er),"Date invalide"}},Ls=()=>{var e;try{const[t,n]=Dt.useState([]),[r,a]=Dt.useState([]),[l,d]=Dt.useState(""),[u,m]=Dt.useState(""),[h,p]=Dt.useState(""),[x,g]=Dt.useState(0),[y,j]=Dt.useState(10),[v,b]=Dt.useState(!1),[S,D]=Dt.useState(null),[f,w]=Dt.useState({description:"",montantCDF:0,categorie:"",dateDepense:Ns(new Date,"yyyy-MM-dd"),notes:""}),[P,E]=Dt.useState(""),[F,k]=Dt.useState(""),[R,M]=Dt.useState({tauxChangeUSDCDF:2800}),[N,L]=Dt.useState(!1),[V,B]=Dt.useState(!1),[z,_]=Dt.useState(null),[$,W]=Dt.useState(!1),X=is.getUserPermissions(),Q=is.getCurrentUser(),H=["Loyer","Électricité","Eau","Internet","Téléphone","Transport","Carburant","Fournitures de bureau","Marketing","Maintenance","Assurance","Taxes","Salaires","Formation","Équipement","Autres"];Dt.useEffect(()=>{Y()},[]),Dt.useEffect(()=>{K()},[t,l,u,h]),Dt.useEffect(()=>{-1===y&&j(r.length||1)},[r.length,y]);const Y=async()=>{const e=await ns.getExpenses(),t=await ns.getSettings();n(e),M(t)},K=()=>{let e=t;if(l&&(e=e.filter(e=>e.description.toLowerCase().includes(l.toLowerCase())||e.categorie.toLowerCase().includes(l.toLowerCase())||e.notes&&e.notes.toLowerCase().includes(l.toLowerCase()))),u&&(e=e.filter(e=>e.categorie===u)),h){const t=new Date;let n,i;switch(h){case"today":n=new Date(t.getFullYear(),t.getMonth(),t.getDate()),i=new Date(t.getFullYear(),t.getMonth(),t.getDate(),23,59,59);break;case"this_month":n=At(t),i=Nt(t);break;case"last_month":const e=new Date(t.getFullYear(),t.getMonth()-1,1);n=At(e),i=Nt(e);break;default:n=new Date(0),i=new Date}e=e.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,{start:n,end:i})})}a(e)},G=e=>{e?(D(e),w({description:e.description,montantCDF:e.montantCDF,categorie:e.categorie,dateDepense:Ns(e.dateDepense,"yyyy-MM-dd"),notes:e.notes||""})):(D(null),w({description:"",montantCDF:0,categorie:"",dateDepense:Ns(new Date,"yyyy-MM-dd"),notes:""})),b(!0),E(""),k("")},Z=()=>{b(!1),D(null),E(""),k(""),L(!1)},se=async()=>{var e;if(f.description.trim())if(f.montantCDF<=0)E("Le montant doit être supérieur à 0");else if(f.categorie)if(f.dateDepense){if(S){const e={...S,description:f.description.trim(),montantCDF:f.montantCDF,montantUSD:f.montantCDF/R.tauxChangeUSDCDF,categorie:f.categorie,dateDepense:f.dateDepense,notes:f.notes.trim()||void 0},i=t.map(t=>t.id===S.id?e:t);n(i),await ns.setExpenses(i),k("Dépense mise à jour avec succès")}else{const s=N||(null==(e=R.impression)?void 0:e.impressionAutomatique)||!1,r=s?await Fs.generateExpenseReceiptNumber():void 0,a={id:Date.now().toString(),description:f.description.trim(),montantCDF:f.montantCDF,montantUSD:f.montantCDF/R.tauxChangeUSDCDF,categorie:f.categorie,dateDepense:f.dateDepense,notes:f.notes.trim()||void 0,creePar:(null==Q?void 0:Q.nom)||"Inconnu",numeroRecu:r},o=[...t,a];if(n(o),await ns.setExpenses(o),k("Dépense créée avec succès"),s)try{W(!0);const e=await Fs.createExpenseReceiptData(a);_(e),B(!0)}catch(i){console.error("Erreur lors de la génération du reçu:",i),E("Erreur lors de la génération du reçu")}finally{W(!1)}}setTimeout(()=>{Z()},1500)}else E("La date est requise");else E("La catégorie est requise");else E("La description est requise")},re=async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer cette dépense "${e.description}" ?`)){const i=t.filter(t=>t.id!==e.id);n(i),await ns.setExpenses(i),k("Dépense supprimée avec succès"),setTimeout(()=>k(""),3e3)}},ae=(e,t)=>{-1!==y&&g(t)},oe=e=>{const t=parseInt(e.target.value,10);j(t),g(0)},le=new Date,de={start:At(le),end:Nt(le)},me=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&t.toDateString()===le.toDateString()}),De=t.filter(e=>{const t=new Date(e.dateDepense);return Lt(t)&&Ot(t,de)}),fe=t.length,Ce=me.reduce((e,t)=>e+t.montantCDF,0),we=De.reduce((e,t)=>e+t.montantCDF,0),Pe=t.reduce((e,t)=>e+t.montantCDF,0),Ee=t.reduce((e,t)=>(e[t.categorie]=(e[t.categorie]||0)+t.montantUSD,e),{}),ke=Object.entries(Ee).sort(([,e],[,t])=>t-e).slice(0,5),Re=()=>{const e=["Description,Catégorie,Montant CDF,Montant USD,Date Dépense,Créé par,Notes",...r.map(e=>{var t;return[`"${e.description}"`,e.categorie,e.montantCDF.toLocaleString("fr-FR"),(null==(t=e.montantUSD)?void 0:t.toFixed(2))||"",Ns(e.dateDepense),e.creePar,`"${e.notes||""}"`].join(",")})].join("\n"),t=new Blob([e],{type:"text/csv;charset=utf-8"}),n=URL.createObjectURL(t),i=document.createElement("a");i.href=n,i.download=`SmartBoutique_Depenses_${Ns(new Date,"yyyy-MM-dd")}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),k("Dépenses exportées en CSV avec succès"),setTimeout(()=>k(""),3e3)};return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Dépenses"}),i.jsxs(s,{display:"flex",gap:2,children:[i.jsx(A,{variant:"outlined",startIcon:i.jsx(Fe,{}),onClick:Re,children:"Exporter les Dépenses"}),X.canManageExpenses&&i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>G(),children:"Nouvelle Dépense"})]})]}),F&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>k(""),children:F}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du jour"}),i.jsx(o,{variant:"h6",children:me.length}),i.jsx(o,{variant:"body2",color:"error",children:Ss(Ce,"CDF")})]}),i.jsx(C,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Dépenses du mois"}),i.jsx(o,{variant:"h6",children:De.length}),i.jsx(o,{variant:"body2",color:"error",children:Ss(we,"CDF")})]}),i.jsx(nt,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",children:fe}),i.jsx(o,{variant:"body2",color:"error",children:Ss(Pe,"CDF")})]}),i.jsx(ue,{color:"info",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Catégories"}),i.jsx(o,{variant:"h6",children:Object.keys(Ee).length}),i.jsxs(o,{variant:"body2",color:"text.secondary",children:["Top: ",(null==(e=ke[0])?void 0:e[0])||"N/A"]})]}),i.jsx(it,{color:"warning",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par description, catégorie ou notes...",value:l,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Catégorie"}),i.jsxs(Se,{value:u,label:"Catégorie",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les catégories"}),H.map(e=>i.jsx(J,{value:e,children:e},e))]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Période"}),i.jsxs(Se,{value:h,label:"Période",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Toutes les périodes"}),i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"})]})]})})]})}),ke.length>0&&i.jsxs(c,{sx:{p:2,mb:3},children:[i.jsx(o,{variant:"h6",gutterBottom:!0,children:"Top 5 Catégories"}),i.jsx(ce,{container:!0,spacing:1,children:ke.map(([e,t])=>i.jsx(ce,{item:!0,children:i.jsx(I,{label:`${e}: ${Ss(t,"CDF")}`,color:"primary",variant:"outlined"})},e))})]}),i.jsxs(he,{component:c,children:[i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Description"}),i.jsx(ye,{children:"Catégorie"}),i.jsx(ye,{align:"right",children:"Montant CDF"}),i.jsx(ye,{align:"right",children:"Montant USD"}),i.jsx(ye,{children:"Date"}),i.jsx(ye,{children:"Créé par"}),X.canManageExpenses&&i.jsx(ye,{align:"center",children:"Actions"})]})}),i.jsx(je,{children:(-1===y?r:r.slice(x*y,x*y+y)).map(e=>i.jsxs(ge,{hover:!0,onClick:()=>G(e),sx:{cursor:"pointer"},children:[i.jsx(ye,{children:i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",children:e.description}),e.notes&&i.jsx(o,{variant:"caption",color:"text.secondary",children:e.notes})]})}),i.jsx(ye,{children:i.jsx(I,{label:e.categorie,size:"small"})}),i.jsx(ye,{align:"right",children:Ss(e.montantCDF,"CDF")}),i.jsx(ye,{align:"right",children:e.montantUSD?Ss(e.montantUSD,"USD"):"-"}),i.jsx(ye,{children:Ns(e.dateDepense)}),i.jsx(ye,{children:e.creePar}),X.canManageExpenses&&i.jsx(ye,{align:"center",children:i.jsxs(s,{display:"flex",gap:1,children:[i.jsx(U,{title:"Modifier",children:i.jsx(T,{size:"small",onClick:()=>G(e),children:i.jsx(Me,{fontSize:"small"})})}),is.hasRole(["super_admin","admin"])&&i.jsx(U,{title:"Supprimer",children:i.jsx(T,{size:"small",color:"error",onClick:()=>re(e),children:i.jsx(q,{fontSize:"small"})})})]})})]},e.id))})]}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:r.length,rowsPerPage:-1===y?r.length:y,page:-1===y?0:x,onPageChange:ae,onRowsPerPageChange:oe,labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:v,onClose:Z,maxWidth:"md",fullWidth:!0,children:[i.jsx(Ne,{children:S?"Modifier la Dépense":"Nouvelle Dépense"}),i.jsxs(Le,{children:[P&&i.jsx(O,{severity:"error",sx:{mb:2},children:P}),F&&i.jsx(O,{severity:"success",sx:{mb:2},children:F}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description *",value:f.description,onChange:e=>w({...f,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Es,{label:"Montant de la dépense",value:f.montantCDF,onChange:e=>w({...f,montantCDF:e}),min:0,max:5e6,step:50,exchangeRate:R.tauxChangeUSDCDF,required:!0,showSlider:!0,allowUSDInput:!0})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Catégorie *"}),i.jsx(Se,{value:f.categorie,label:"Catégorie *",onChange:e=>w({...f,categorie:e.target.value}),children:H.map(e=>i.jsx(J,{value:e,children:e},e))})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Date de la dépense *",type:"date",value:f.dateDepense,onChange:e=>w({...f,dateDepense:e.target.value}),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Notes",multiline:!0,rows:3,value:f.notes,onChange:e=>w({...f,notes:e.target.value})})})]})]}),i.jsxs(Oe,{children:[!S&&i.jsx(Qe,{control:i.jsx(He,{checked:N,onChange:e=>L(e.target.checked)}),label:"Imprimer reçu",sx:{mr:"auto"}}),$&&i.jsxs(s,{display:"flex",alignItems:"center",gap:1,mr:2,children:[i.jsx(Be,{size:20}),i.jsx(o,{variant:"body2",children:"Génération du reçu..."})]}),i.jsx(A,{onClick:Z,children:"Annuler"}),i.jsx(A,{onClick:se,variant:"contained",disabled:$,children:S?"Mettre à jour":"Créer"})]})]}),i.jsx(Ms,{open:V,onClose:()=>B(!1),receiptData:z,onPrintSuccess:()=>{k("Reçu imprimé avec succès"),setTimeout(()=>k(""),3e3)}})]})}catch(er){return console.error("ExpensesPage error:",er),i.jsxs(s,{p:3,children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Dépenses"}),i.jsx(O,{severity:"error",children:"Une erreur s'est produite lors du chargement de la page des dépenses. Veuillez recharger l'application ou contacter le support technique."})]})}};function Os(e){const{children:t,value:n,index:r,...a}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==r,id:`reports-tabpanel-${r}`,"aria-labelledby":`reports-tab-${r}`,...a,children:n===r&&i.jsx(s,{sx:{p:3},children:t})})}const Vs=()=>{const[e,t]=Dt.useState(0),[n,r]=Dt.useState("this_month"),[a,l]=Dt.useState(""),[d,u]=Dt.useState(""),[m,p]=Dt.useState([]),[x,g]=Dt.useState([]),[y,j]=Dt.useState([]),[D,f]=Dt.useState([]),[P,E]=Dt.useState([]),[U,T]=Dt.useState([]);Dt.useEffect(()=>{F()},[]),Dt.useEffect(()=>{k()},[m,D,n,a,d]);const F=async()=>{p(await ns.getSales()),g(await ns.getProducts()),j(await ns.getDebts()),f(await ns.getExpenses())},k=()=>{const e=new Date;let t,i;switch(n){case"today":t=Ft(e),i=kt(e);break;case"this_week":t=Rt(e,7),i=e;break;case"this_month":default:t=At(e),i=Nt(e);break;case"last_month":const n=new Date(e.getFullYear(),e.getMonth()-1,1);t=At(n),i=Nt(n);break;case"custom":a&&d?(t=new Date(a),i=new Date(d)):(t=At(e),i=Nt(e))}const s=m.filter(e=>{const n=new Date(e.datevente);return Ot(n,{start:t,end:i})}),r=D.filter(e=>{const n=new Date(e.dateDepense);return Ot(n,{start:t,end:i})});E(s),T(r)},R=(e,t)=>"USD"===t?`$${e.toLocaleString("fr-FR",{minimumFractionDigits:2})}`:`${e.toLocaleString("fr-FR")} CDF`,M=P.reduce((e,t)=>e+t.totalCDF,0),N=P.length,L=N>0?M/N:0,O=U.reduce((e,t)=>e+t.montantCDF,0),V=M-O,q=P.reduce((e,t)=>(t.produits.forEach(t=>{e[t.produitId]||(e[t.produitId]={nom:t.nomProduit,quantite:0,revenue:0}),e[t.produitId].quantite+=t.quantite,e[t.produitId].revenue+=t.totalCDF}),e),{}),B=Object.values(q).sort((e,t)=>t.revenue-e.revenue).slice(0,10),z=P.reduce((e,t)=>(e[t.methodePaiement]=(e[t.methodePaiement]||0)+t.totalCDF,e),{}),_=P.reduce((e,t)=>(t.produits.forEach(t=>{const n=x.find(e=>e.id===t.produitId);n&&(e[n.categorie]=(e[n.categorie]||0)+t.totalCDF)}),e),{}),$=P.reduce((e,t)=>{const n=Tt(new Date(t.datevente),"yyyy-MM-dd");return e[n]=(e[n]||0)+t.totalCDF,e},{}),W=Array.from({length:30},(e,t)=>{const n=Rt(new Date,29-t),i=Tt(n,"yyyy-MM-dd");return{date:Tt(n,"dd/MM"),revenue:$[i]||0}}),X={labels:W.map(e=>e.date),datasets:[{label:"Revenus (USD)",data:W.map(e=>e.revenue),borderColor:"rgb(75, 192, 192)",backgroundColor:"rgba(75, 192, 192, 0.2)",tension:.1}]},Q={labels:["Espèces","Carte","Mobile Money"],datasets:[{data:[z.cash||0,z.card||0,z.mobile_money||0],backgroundColor:["#FF6384","#36A2EB","#FFCE56"]}]},H={labels:Object.keys(_),datasets:[{label:"Revenus par catégorie (USD)",data:Object.values(_),backgroundColor:["#FF6384","#36A2EB","#FFCE56","#4BC0C0","#9966FF","#FF9F40"]}]};return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Rapports et Analyses"}),i.jsx(A,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:()=>{const e=`Résumé du Rapport\nPériode,${"custom"===n?`${a} - ${d}`:n}\nDate de Génération,${(new Date).toISOString()}\nTotal Ventes,${N}\nChiffre d'Affaires (USD),${M.toFixed(2)}\nTotal Dépenses (USD),${O.toFixed(2)}\nBénéfice Net (USD),${V.toFixed(2)}\nVente Moyenne (USD),${L.toFixed(2)}\n`,t=`\nTop Produits les Plus Vendus\nRang,Produit,Quantité Vendue,Revenus (USD),Revenus Moyens (USD)\n${B.map((e,t)=>`${t+1},${e.nom},${e.quantite},${e.revenue.toFixed(2)},${(e.revenue/e.quantite).toFixed(2)}`).join("\n")}\n`,i=`\nMéthodes de Paiement\nMéthode,Nombre de Transactions,Montant Total (USD)\n${z.map(e=>`${e.method},${e.count},${e.amount.toFixed(2)}`).join("\n")}\n`,s=`\nPerformance par Catégorie\nCatégorie,Revenus (USD),Nombre de Ventes\n${_.map(e=>`${e.category},${e.revenue.toFixed(2)},${e.sales}`).join("\n")}\n`,r=new Blob([`SmartBoutique - Rapport d'Analyse\n${e}${t}${i}${s}`],{type:"text/csv;charset=utf-8"}),o=URL.createObjectURL(r),l=document.createElement("a");l.href=o,l.download=`SmartBoutique_Rapport_${n}_${Tt(new Date,"yyyy-MM-dd")}.csv`,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(o)},children:"Exporter le Rapport"})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Période"}),i.jsxs(Se,{value:n,label:"Période",onChange:e=>r(e.target.value),children:[i.jsx(J,{value:"today",children:"Aujourd'hui"}),i.jsx(J,{value:"this_week",children:"Cette semaine"}),i.jsx(J,{value:"this_month",children:"Ce mois"}),i.jsx(J,{value:"last_month",children:"Mois dernier"}),i.jsx(J,{value:"custom",children:"Personnalisée"})]})]})}),"custom"===n&&i.jsxs(i.Fragment,{children:[i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de début",type:"date",value:a,onChange:e=>l(e.target.value),InputLabelProps:{shrink:!0}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Date de fin",type:"date",value:d,onChange:e=>u(e.target.value),InputLabelProps:{shrink:!0}})})]})]})}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Chiffre d'Affaires"}),i.jsx(o,{variant:"h6",color:"primary",children:R(M,"USD")})]}),i.jsx(De,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Nombre de Ventes"}),i.jsx(o,{variant:"h6",color:"success.main",children:N})]}),i.jsx(b,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Dépenses"}),i.jsx(o,{variant:"h6",color:"error",children:R(O,"USD")})]}),i.jsx(C,{color:"error",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Bénéfice Net"}),i.jsx(o,{variant:"h6",color:V>=0?"success.main":"error",children:R(V,"USD")})]}),i.jsx(S,{color:V>=0?"success":"error",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{mb:3},children:i.jsxs(st,{value:e,onChange:(e,n)=>{t(n)},"aria-label":"reports tabs",children:[i.jsx(rt,{label:"Tendances",icon:i.jsx(De,{})}),i.jsx(rt,{label:"Produits",icon:i.jsx(v,{})}),i.jsx(rt,{label:"Analyses",icon:i.jsx(w,{})})]})}),i.jsx(Os,{value:e,index:0,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:8,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Évolution des Ventes (30 derniers jours)"}),i.jsx(te,{children:i.jsx(Vt,{data:X,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})}),i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Méthodes de Paiement"}),i.jsx(te,{children:i.jsx(qt,{data:Q,options:{responsive:!0,plugins:{legend:{position:"bottom"}}}})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Performance par Catégorie"}),i.jsx(te,{children:i.jsx(Bt,{data:H,options:{responsive:!0,plugins:{legend:{position:"top"}},scales:{y:{beginAtZero:!0}}}})})]})})]})}),i.jsx(Os,{value:e,index:1,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Top 10 Produits les Plus Vendus"}),i.jsx(te,{children:i.jsx(he,{children:i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Rang"}),i.jsx(ye,{children:"Produit"}),i.jsx(ye,{align:"right",children:"Quantité Vendue"}),i.jsx(ye,{align:"right",children:"Revenus"}),i.jsx(ye,{align:"right",children:"Revenus Moyens"})]})}),i.jsx(je,{children:B.map((e,t)=>i.jsxs(ge,{children:[i.jsx(ye,{children:i.jsx(I,{label:`#${t+1}`,color:t<3?"primary":"default",size:"small"})}),i.jsx(ye,{children:e.nom}),i.jsx(ye,{align:"right",children:e.quantite}),i.jsx(ye,{align:"right",children:R(e.revenue,"USD")}),i.jsx(ye,{align:"right",children:R(e.revenue/e.quantite,"USD")})]},t))})]})})})]})}),i.jsx(Os,{value:e,index:2,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Métriques de Performance"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Vente Moyenne"}),i.jsx(o,{variant:"h6",children:R(L,"USD")})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Produits Vendus"}),i.jsx(o,{variant:"h6",children:P.reduce((e,t)=>e+t.produits.reduce((e,t)=>e+t.quantite,0),0)})]}),i.jsxs(ce,{item:!0,xs:6,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Panier Moyen"}),i.jsxs(o,{variant:"h6",children:[N>0?(P.reduce((e,t)=>e+t.produits.length,0)/N).toFixed(1):"0"," articles"]})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Répartition des Revenus"}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes Cash"}),i.jsx(o,{variant:"h6",color:"success.main",children:R(P.filter(e=>"cash"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Ventes à Crédit"}),i.jsx(o,{variant:"h6",color:"warning.main",children:R(P.filter(e=>"credit"===e.typeVente).reduce((e,t)=>e+t.totalCDF,0),"CDF")})]}),i.jsx(h,{sx:{width:"100%",my:1}}),i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(o,{variant:"body2",color:"text.secondary",children:"Créances Actives"}),i.jsx(o,{variant:"h6",color:"error",children:R(y.filter(e=>"paid"!==e.statut).reduce((e,t)=>e+t.montantRestantCDF,0),"CDF")})]})]})})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Analyse des Dépenses par Catégorie"}),i.jsx(te,{children:i.jsx(he,{children:i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Catégorie"}),i.jsx(ye,{align:"right",children:"Nombre"}),i.jsx(ye,{align:"right",children:"Montant Total"}),i.jsx(ye,{align:"right",children:"Montant Moyen"}),i.jsx(ye,{align:"right",children:"% du Total"})]})}),i.jsx(je,{children:Object.entries(U.reduce((e,t)=>(e[t.categorie]||(e[t.categorie]={count:0,total:0}),e[t.categorie].count+=1,e[t.categorie].total+=t.montantCDF,e),{})).sort(([,e],[,t])=>t.total-e.total).map(([e,t])=>i.jsxs(ge,{children:[i.jsx(ye,{children:e}),i.jsx(ye,{align:"right",children:t.count}),i.jsx(ye,{align:"right",children:R(t.total,"USD")}),i.jsx(ye,{align:"right",children:R(t.total/t.count,"USD")}),i.jsx(ye,{align:"right",children:O>0?`${(t.total/O*100).toFixed(1)}%`:"0%"})]},e))})]})})})]})})]})})]})},qs=()=>{const[e,t]=Dt.useState([]),[n,r]=Dt.useState([]),[a,d]=Dt.useState(""),[u,m]=Dt.useState(""),[h,p]=Dt.useState(""),[x,g]=Dt.useState(0),[y,j]=Dt.useState(10),[v,b]=Dt.useState(!1),[S,D]=Dt.useState(null),[f,C]=Dt.useState(!1),[w,E]=Dt.useState({nom:"",email:"",role:"employee",motDePasse:"",actif:!0}),[F,k]=Dt.useState(""),[R,M]=Dt.useState(""),N=is.getUserPermissions(),L=is.getCurrentUser();Dt.useEffect(()=>{V()},[]),Dt.useEffect(()=>{B()},[e,a,u,h]),Dt.useEffect(()=>{-1===y&&j(n.length||1)},[n.length,y]);const V=async()=>{const e=await ns.getUsers();t(e)},B=()=>{let t=e;if(a&&(t=t.filter(e=>e.nom.toLowerCase().includes(a.toLowerCase())||e.email.toLowerCase().includes(a.toLowerCase()))),u&&(t=t.filter(e=>e.role===u)),h){const e="active"===h;t=t.filter(t=>t.actif===e)}r(t)},z=e=>{switch(e){case"super_admin":return"Super Admin";case"admin":return"Administrateur";case"employee":return"Employé";default:return e}},_=e=>{switch(e){case"super_admin":return"error";case"admin":return"warning";case"employee":return"primary";default:return"default"}},$=e=>{switch(e){case"super_admin":case"admin":return i.jsx(ot,{});default:return i.jsx(lt,{})}},W=e=>{e?(D(e),E({nom:e.nom,email:e.email,role:e.role,motDePasse:"",actif:e.actif})):(D(null),E({nom:"",email:"",role:"employee",motDePasse:"",actif:!0})),b(!0),C(!1),k(""),M("")},X=()=>{b(!1),D(null),k(""),M("")},Q=e.length,H=e.filter(e=>e.actif).length,Y=e.filter(e=>"admin"===e.role||"super_admin"===e.role).length,G=e.filter(e=>"employee"===e.role).length;return i.jsxs(s,{children:[i.jsxs(s,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[i.jsx(o,{variant:"h4",children:"Gestion des Utilisateurs"}),N.canManageUsers&&i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>W(),children:"Nouvel Utilisateur"})]}),R&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>M(""),children:R}),F&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>k(""),children:F}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Total Utilisateurs"}),i.jsx(o,{variant:"h6",children:Q})]}),i.jsx(P,{color:"primary",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Utilisateurs Actifs"}),i.jsx(o,{variant:"h6",color:"success.main",children:H})]}),i.jsx(at,{color:"success",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Administrateurs"}),i.jsx(o,{variant:"h6",color:"warning.main",children:Y})]}),i.jsx(ot,{color:"warning",sx:{fontSize:40}})]})})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsx(te,{children:i.jsxs(s,{display:"flex",alignItems:"center",justifyContent:"space-between",children:[i.jsxs(s,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,variant:"body2",children:"Employés"}),i.jsx(o,{variant:"h6",color:"info.main",children:G})]}),i.jsx(lt,{color:"info",sx:{fontSize:40}})]})})})})]}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,md:4,children:i.jsx(ne,{fullWidth:!0,placeholder:"Rechercher par nom ou email...",value:a,onChange:e=>d(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Rôle"}),i.jsxs(Se,{value:u,label:"Rôle",onChange:e=>m(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les rôles"}),i.jsx(J,{value:"super_admin",children:"Super Admin"}),i.jsx(J,{value:"admin",children:"Administrateur"}),i.jsx(J,{value:"employee",children:"Employé"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:3,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Statut"}),i.jsxs(Se,{value:h,label:"Statut",onChange:e=>p(e.target.value),children:[i.jsx(J,{value:"",children:"Tous les statuts"}),i.jsx(J,{value:"active",children:"Actif"}),i.jsx(J,{value:"inactive",children:"Inactif"})]})]})})]})}),i.jsxs(he,{component:c,children:[i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Utilisateur"}),i.jsx(ye,{children:"Email"}),i.jsx(ye,{align:"center",children:"Rôle"}),i.jsx(ye,{align:"center",children:"Statut"}),i.jsx(ye,{children:"Date de Création"}),N.canManageUsers&&i.jsx(ye,{align:"center",children:"Actions"})]})}),i.jsx(je,{children:(-1===y?n:n.slice(x*y,x*y+y)).map(n=>i.jsxs(ge,{hover:!0,onClick:()=>W(n),sx:{cursor:"pointer"},children:[i.jsx(ye,{children:i.jsxs(s,{display:"flex",alignItems:"center",gap:2,children:[i.jsx(l,{sx:{bgcolor:_(n.role)},children:n.nom.charAt(0).toUpperCase()}),i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",children:n.nom}),n.id===(null==L?void 0:L.id)&&i.jsx(I,{label:"Vous",size:"small",color:"primary"})]})]})}),i.jsx(ye,{children:n.email}),i.jsx(ye,{align:"center",children:i.jsx(I,{icon:$(n.role),label:z(n.role),color:_(n.role),size:"small"})}),i.jsx(ye,{align:"center",children:i.jsx(I,{label:n.actif?"Actif":"Inactif",color:n.actif?"success":"error",size:"small"})}),i.jsx(ye,{children:Tt(new Date(n.dateCreation),"dd/MM/yyyy",{locale:cs})}),N.canManageUsers&&i.jsx(ye,{align:"center",children:i.jsxs(s,{display:"flex",gap:1,children:[i.jsx(U,{title:"Modifier",children:i.jsx(T,{size:"small",onClick:()=>W(n),disabled:"super_admin"===n.role&&!is.hasRole(["super_admin"]),children:i.jsx(Me,{fontSize:"small"})})}),i.jsx(U,{title:n.actif?"Désactiver":"Activer",children:i.jsx(T,{size:"small",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id)&&n.actif)return k("Vous ne pouvez pas désactiver votre propre compte"),void setTimeout(()=>k(""),3e3);const i={...n,actif:!n.actif},s=e.map(e=>e.id===n.id?i:e);t(s),await ns.setUsers(s),M(`Utilisateur ${i.actif?"activé":"désactivé"} avec succès`),setTimeout(()=>M(""),3e3)})(n),disabled:n.id===(null==L?void 0:L.id)&&n.actif,children:n.actif?i.jsx(K,{fontSize:"small"}):i.jsx(at,{fontSize:"small"})})}),is.hasRole(["super_admin"])&&i.jsx(U,{title:"Supprimer",children:i.jsx(T,{size:"small",color:"error",onClick:()=>(async n=>{if(n.id===(null==L?void 0:L.id))return k("Vous ne pouvez pas supprimer votre propre compte"),void setTimeout(()=>k(""),3e3);if("super_admin"===n.role&&!is.hasRole(["super_admin"]))return k("Seul un Super Admin peut supprimer un compte Super Admin"),void setTimeout(()=>k(""),3e3);if(window.confirm(`Êtes-vous sûr de vouloir supprimer l'utilisateur "${n.nom}" ?`)){const i=e.filter(e=>e.id!==n.id);t(i),await ns.setUsers(i),M("Utilisateur supprimé avec succès"),setTimeout(()=>M(""),3e3)}})(n),disabled:n.id===(null==L?void 0:L.id),children:i.jsx(q,{fontSize:"small"})})})]})})]},n.id))})]}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100,{label:"Voir tout",value:-1}],component:"div",count:n.length,rowsPerPage:-1===y?n.length:y,page:-1===y?0:x,onPageChange:(e,t)=>{-1!==y&&g(t)},onRowsPerPageChange:e=>{const t=parseInt(e.target.value,10);j(t),g(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>-1===y?`Affichage de tous les ${n} éléments`:`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:v,onClose:X,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ne,{children:S?"Modifier l'Utilisateur":"Nouvel Utilisateur"}),i.jsxs(Le,{children:[F&&i.jsx(O,{severity:"error",sx:{mb:2},children:F}),R&&i.jsx(O,{severity:"success",sx:{mb:2},children:R}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom complet *",value:w.nom,onChange:e=>E({...w,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Email *",type:"email",value:w.email,onChange:e=>E({...w,email:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Rôle *"}),i.jsxs(Se,{value:w.role,label:"Rôle *",onChange:e=>E({...w,role:e.target.value}),disabled:!is.hasRole(["super_admin"])||!(!S||S.id!==(null==L?void 0:L.id)),children:[i.jsx(J,{value:"employee",children:"Employé"}),i.jsx(J,{value:"admin",children:"Administrateur"}),is.hasRole(["super_admin"])&&i.jsx(J,{value:"super_admin",children:"Super Admin"})]})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:S?"Nouveau mot de passe (optionnel)":"Mot de passe *",type:f?"text":"password",value:w.motDePasse,onChange:e=>E({...w,motDePasse:e.target.value}),InputProps:{endAdornment:i.jsx(ie,{position:"end",children:i.jsx(T,{onClick:()=>C(!f),edge:"end",children:f?i.jsx(re,{}):i.jsx(ae,{})})})},helperText:S?"Laissez vide pour conserver le mot de passe actuel":"Minimum 6 caractères"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Qe,{control:i.jsx(Ze,{checked:w.actif,onChange:e=>E({...w,actif:e.target.checked}),disabled:!(!S||S.id!==(null==L?void 0:L.id))}),label:"Compte actif"})})]})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:X,children:"Annuler"}),i.jsx(A,{onClick:async()=>{if(!w.nom.trim())return void k("Le nom est requis");if(!w.email.trim())return void k("L'email est requis");if(!S&&!w.motDePasse.trim())return void k("Le mot de passe est requis pour un nouvel utilisateur");if(w.motDePasse&&w.motDePasse.length<6)return void k("Le mot de passe doit contenir au moins 6 caractères");if(e.some(e=>e.email.toLowerCase()===w.email.trim().toLowerCase()&&e.id!==(null==S?void 0:S.id)))return void k("Un utilisateur avec cet email existe déjà");if("super_admin"===w.role&&!is.hasRole(["super_admin"]))return void k("Seul un Super Admin peut créer ou modifier un compte Super Admin");if(S&&S.id===(null==L?void 0:L.id)&&w.role!==S.role)return void k("Vous ne pouvez pas modifier votre propre rôle");if(S&&S.id===(null==L?void 0:L.id)&&!w.actif)return void k("Vous ne pouvez pas désactiver votre propre compte");const n=(new Date).toISOString();if(S){const n={...S,nom:w.nom.trim(),email:w.email.trim(),role:w.role,actif:w.actif,...w.motDePasse&&{motDePasse:w.motDePasse}},i=e.map(e=>e.id===S.id?n:e);t(i),await ns.setUsers(i),S.id===(null==L?void 0:L.id)&&await ns.setCurrentUser(n),M("Utilisateur mis à jour avec succès")}else{const i={id:Date.now().toString(),nom:w.nom.trim(),email:w.email.trim(),role:w.role,motDePasse:w.motDePasse,dateCreation:n,actif:w.actif},s=[...e,i];t(s),await ns.setUsers(s),M("Utilisateur créé avec succès")}setTimeout(()=>{X()},1500)},variant:"contained",children:S?"Mettre à jour":"Créer"})]})]})]})};class Bs{async exportAllData(){try{const[e,t,n,i,s,r]=await Promise.all([Wi.getProducts(),Wi.getUsers(),Wi.getSales(),Wi.getDebts(),Wi.getExpenses(),Wi.getSettings()]),a={exportDate:(new Date).toISOString(),products:Ii.arrayToCSV(e,Ai),users:Ii.arrayToCSV(t,Ni),sales:Ii.arrayToCSV(n,Li),debts:Ii.arrayToCSV(i,Oi),expenses:Ii.arrayToCSV(s,Vi),settings:Ii.arrayToCSV(zi(r),Bi)};return{success:!0,message:"Exportation complète réussie",data:`SmartBoutique - Sauvegarde Complète\nDate d'exportation: ${a.exportDate}\n\n=== PRODUITS ===\n${a.products}\n\n=== UTILISATEURS ===\n${a.users}\n\n=== VENTES ===\n${a.sales}\n\n=== DETTES ===\n${a.debts}\n\n=== DÉPENSES ===\n${a.expenses}\n\n=== PARAMÈTRES ===\n${a.settings}\n`}}catch(er){return console.error("Erreur lors de l'exportation complète:",er),{success:!1,message:"Erreur lors de l'exportation: "+er.message}}}async exportData(e){try{let t=[],n=[],i="";switch(e){case"products":t=await Wi.getProducts(),n=Ai,i="produits";break;case"users":t=await Wi.getUsers(),n=Ni,i="utilisateurs";break;case"sales":t=await Wi.getSales(),n=Li,i="ventes";break;case"debts":t=await Wi.getDebts(),n=Oi,i="dettes";break;case"expenses":t=await Wi.getExpenses(),n=Vi,i="depenses"}const s=Ii.arrayToCSV(t,n);return{success:!0,message:`Exportation ${i} réussie (${t.length} enregistrements)`,data:s}}catch(er){return console.error(`Erreur lors de l'exportation ${e}:`,er),{success:!1,message:"Erreur lors de l'exportation: "+er.message}}}async importProducts(e,t=!1){try{const n=Ii.csvToArray(e,Ai),i=Ii.validateCSVData(n,Ai);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let s=n;if(!t){const e=await Wi.getProducts(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));s=[...e,...i]}return await Wi.setProducts(s),{success:!0,message:`${n.length} produits importés avec succès`,errors:[],importedCount:n.length}}catch(er){return console.error("Erreur lors de l'importation des produits:",er),{success:!1,message:"Erreur lors de l'importation: "+er.message,errors:[er.message],importedCount:0}}}async importUsers(e,t=!1){try{const n=Ii.csvToArray(e,Ni),i=Ii.validateCSVData(n,Ni);if(!i.isValid)return{success:!1,message:"Données invalides détectées",errors:i.errors,importedCount:0};let s=n;if(!t){const e=await Wi.getUsers(),t=new Set(e.map(e=>e.id)),i=n.filter(e=>!t.has(e.id));s=[...e,...i]}return await Wi.setUsers(s),{success:!0,message:`${n.length} utilisateurs importés avec succès`,errors:[],importedCount:n.length}}catch(er){return console.error("Erreur lors de l'importation des utilisateurs:",er),{success:!1,message:"Erreur lors de l'importation: "+er.message,errors:[er.message],importedCount:0}}}generateTemplate(e){switch(e){case"products":return Ii.generateTemplate(Ai);case"users":return Ii.generateTemplate(Ni);case"sales":return Ii.generateTemplate(Li);case"debts":return Ii.generateTemplate(Oi);case"expenses":return Ii.generateTemplate(Vi);default:return""}}async createAutomaticBackup(){try{const e=await this.exportAllData();if(e.success&&e.data){const t=(new Date).toISOString().replace(/[:.]/g,"-");return{success:!0,message:`Sauvegarde automatique créée: ${t}`,data:e.data}}return e}catch(er){return console.error("Erreur lors de la sauvegarde automatique:",er),{success:!1,message:"Erreur lors de la sauvegarde automatique: "+er.message}}}getSampleCSVData(e){switch(e){case"products":return"ID,Nom du Produit,Description,Prix CDF,Prix USD,Code QR,Catégorie,Stock,Stock Minimum,Code Barres,Date de Création,Date de Modification\nSAMPLE1,Produit Exemple,Description du produit exemple,5600,2,SAMPLE123,Alimentation,50,10,1234567890,2024-01-01,2024-01-01";case"users":return"ID,Nom,Email,Rôle,Mot de Passe,Date de Création,Actif\nSAMPLE1,Utilisateur Exemple,<EMAIL>,employee,motdepasse123,2024-01-01,Oui";default:return this.generateTemplate(e)}}}const zs=new Bs,_s=Object.freeze(Object.defineProperty({__proto__:null,CSVImportExportService:Bs,csvImportExportService:zs},Symbol.toStringTag,{value:"Module"})),$s=({onSuccess:e,onError:t})=>{var n,r,a,l;const[c,d]=Dt.useState(!1),[u,m]=Dt.useState(!1),[p,x]=Dt.useState(!1),[g,y]=Dt.useState("products"),[j,v]=Dt.useState(""),[b,S]=Dt.useState(!1),[D,f]=Dt.useState(""),C=[{key:"products",label:"Produits",icon:"📦"},{key:"users",label:"Utilisateurs",icon:"👥"},{key:"sales",label:"Ventes",icon:"💰"},{key:"debts",label:"Dettes",icon:"📋"},{key:"expenses",label:"Dépenses",icon:"💸"}],w=()=>{const e=zs.generateTemplate(g);v(e)};return i.jsx(ee,{children:i.jsxs(te,{children:[i.jsxs(o,{variant:"h6",gutterBottom:!0,children:[i.jsx(ct,{sx:{mr:1,verticalAlign:"middle"}}),"Gestion des Données CSV"]}),i.jsx(o,{variant:"body2",color:"text.secondary",paragraph:!0,children:"Exportez et importez vos données au format CSV pour une meilleure portabilité et accessibilité."}),i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(s,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(Fe,{sx:{mr:1,verticalAlign:"middle"}}),"Exportation"]}),i.jsx(A,{variant:"contained",color:"primary",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await zs.exportAllData();if(n.success&&n.data){const t=new Blob([n.data],{type:"text/plain;charset=utf-8"}),i=URL.createObjectURL(t),s=document.createElement("a");s.href=i,s.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.txt`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),null==e||e("Sauvegarde complète exportée avec succès")}else null==t||t(n.message)}catch(er){null==t||t("Erreur lors de l'exportation: "+er.message)}finally{d(!1)}},disabled:c,startIcon:c?i.jsx(Be,{size:20}):i.jsx(dt,{}),sx:{mb:2},children:"Exporter Toutes les Données"}),i.jsx(h,{sx:{my:2}}),i.jsx(o,{variant:"body2",gutterBottom:!0,children:"Exporter un type de données spécifique:"}),i.jsx(s,{sx:{mb:2},children:C.map(e=>i.jsx(I,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(A,{variant:"outlined",fullWidth:!0,onClick:async()=>{d(!0);try{const n=await zs.exportData(g);n.success&&n.data?(f(n.data),m(!0),null==e||e(n.message)):null==t||t(n.message)}catch(er){null==t||t("Erreur lors de l'exportation: "+er.message)}finally{d(!1)}},disabled:c,startIcon:i.jsx(ut,{}),children:["Exporter ",null==(n=C.find(e=>e.key===g))?void 0:n.label]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(s,{sx:{p:2,border:"1px solid #e0e0e0",borderRadius:1},children:[i.jsxs(o,{variant:"subtitle1",gutterBottom:!0,children:[i.jsx(ke,{sx:{mr:1,verticalAlign:"middle"}}),"Importation"]}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Sélectionnez le type de données à importer:"}),i.jsx(s,{sx:{mb:2},children:C.filter(e=>["products","users"].includes(e.key)).map(e=>i.jsx(I,{label:`${e.icon} ${e.label}`,onClick:()=>y(e.key),color:g===e.key?"primary":"default",sx:{m:.5}},e.key))}),i.jsxs(A,{variant:"outlined",fullWidth:!0,onClick:()=>x(!0),startIcon:i.jsx(ke,{}),sx:{mb:1},children:["Importer ",null==(r=C.find(e=>e.key===g))?void 0:r.label]}),i.jsx(A,{variant:"text",size:"small",fullWidth:!0,onClick:w,children:"Obtenir un modèle CSV"})]})})]}),i.jsxs(Ae,{open:u,onClose:()=>m(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Ne,{children:["Données Exportées - ",null==(a=C.find(e=>e.key===g))?void 0:a.label]}),i.jsx(Le,{children:i.jsx(ne,{multiline:!0,rows:10,fullWidth:!0,value:D,variant:"outlined",InputProps:{readOnly:!0},sx:{fontFamily:"monospace"}})}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:()=>m(!1),children:"Fermer"}),i.jsx(A,{onClick:()=>{var e;const t=(null==(e=C.find(e=>e.key===g))?void 0:e.label)||g,n=new Blob([D],{type:"text/csv;charset=utf-8"}),i=URL.createObjectURL(n),s=document.createElement("a");s.href=i,s.download=`SmartBoutique_${t}_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(i),m(!1)},variant:"contained",startIcon:i.jsx(Fe,{}),children:"Télécharger CSV"})]})]}),i.jsxs(Ae,{open:p,onClose:()=>x(!1),maxWidth:"md",fullWidth:!0,children:[i.jsxs(Ne,{children:["Importer ",null==(l=C.find(e=>e.key===g))?void 0:l.label]}),i.jsxs(Le,{children:[i.jsx(O,{severity:"info",sx:{mb:2},children:'Collez le contenu CSV ci-dessous. Utilisez "Obtenir un modèle CSV" pour voir le format requis.'}),i.jsx(ne,{multiline:!0,rows:8,fullWidth:!0,value:j,onChange:e=>v(e.target.value),placeholder:"Collez votre contenu CSV ici...",variant:"outlined",sx:{mb:2,fontFamily:"monospace"}}),i.jsx(Qe,{control:i.jsx(He,{checked:b,onChange:e=>S(e.target.checked)}),label:"Remplacer les données existantes"})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:()=>x(!1),children:"Annuler"}),i.jsx(A,{onClick:w,variant:"outlined",children:"Obtenir Modèle"}),i.jsx(A,{onClick:async()=>{if(j.trim()){d(!0);try{let n;switch(g){case"products":n=await zs.importProducts(j,b);break;case"users":n=await zs.importUsers(j,b);break;default:return void(null==t||t("Type de données non supporté pour l'importation"))}n.success?(null==e||e(n.message),x(!1),v("")):null==t||t(n.message+"\n"+n.errors.join("\n"))}catch(er){null==t||t("Erreur lors de l'importation: "+er.message)}finally{d(!1)}}else null==t||t("Veuillez saisir le contenu CSV à importer")},variant:"contained",disabled:c||!j.trim(),startIcon:c?i.jsx(Be,{size:20}):i.jsx(ke,{}),children:"Importer"})]})]})]})})},Ws=({value:e="",onChange:t,disabled:n=!1,maxSizeKB:r=500,acceptedFormats:a=["image/jpeg","image/jpg","image/png","image/gif"]})=>{const[l,c]=Dt.useState(""),[d,u]=Dt.useState(!1),m=Dt.useRef(null),h=()=>{m.current&&m.current.click()};return i.jsxs(s,{children:[i.jsx(o,{variant:"subtitle2",gutterBottom:!0,children:"Logo de l'entreprise"}),i.jsx("input",{ref:m,type:"file",accept:a.join(","),onChange:e=>{var n;const i=null==(n=e.target.files)?void 0:n[0];if(!i)return;if(c(""),u(!0),!a.includes(i.type))return c(`Format non supporté. Formats acceptés: ${a.map(e=>e.split("/")[1].toUpperCase()).join(", ")}`),void u(!1);if(i.size/1024>r)return c(`Fichier trop volumineux. Taille maximale: ${r}KB`),void u(!1);const s=new FileReader;s.onload=e=>{var n;const i=null==(n=e.target)?void 0:n.result;i&&t(i),u(!1)},s.onerror=()=>{c("Erreur lors de la lecture du fichier"),u(!1)},s.readAsDataURL(i)},style:{display:"none"},disabled:n}),i.jsx(ee,{variant:"outlined",sx:{mb:2},children:i.jsx(te,{sx:{textAlign:"center",py:3},children:e?i.jsxs(s,{children:[i.jsx(s,{component:"img",src:e,alt:"Logo de l'entreprise",sx:{maxWidth:"200px",maxHeight:"100px",objectFit:"contain",border:"1px solid #e0e0e0",borderRadius:1,mb:2}}),i.jsxs(s,{children:[i.jsx(A,{variant:"outlined",startIcon:i.jsx(mt,{}),onClick:h,disabled:n||d,sx:{mr:1},children:"Changer"}),i.jsx(T,{color:"error",onClick:()=>{t(""),c(""),m.current&&(m.current.value="")},disabled:n||d,title:"Supprimer le logo",children:i.jsx(q,{})})]})]}):i.jsxs(s,{children:[i.jsx(ht,{sx:{fontSize:48,color:"text.secondary",mb:2}}),i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Aucun logo configuré"}),i.jsx(A,{variant:"contained",startIcon:d?i.jsx(Be,{size:20}):i.jsx(mt,{}),onClick:h,disabled:n||d,children:d?"Chargement...":"Télécharger un logo"})]})})}),l&&i.jsx(O,{severity:"error",sx:{mb:2},children:l}),i.jsxs(o,{variant:"caption",color:"text.secondary",children:["Formats acceptés: ",a.map(e=>e.split("/")[1].toUpperCase()).join(", ")," • Taille maximale: ",r,"KB • Recommandé: 200x100px pour un affichage optimal sur les reçus thermiques"]})]})};function Xs(e){const{children:t,value:n,index:r,...a}=e;return i.jsx("div",{role:"tabpanel",hidden:n!==r,id:`settings-tabpanel-${r}`,"aria-labelledby":`settings-tab-${r}`,...a,children:n===r&&i.jsx(s,{sx:{p:3},children:t})})}const Qs=()=>{const[e,t]=Dt.useState(null),[n,r]=Dt.useState(0),[a,l]=Dt.useState(""),[d,u]=Dt.useState(""),[m,g]=Dt.useState({nom:"",adresse:"",telephone:"",email:"",rccm:"",idNat:"",logo:""}),[j,v]=Dt.useState({tauxChangeUSDCDF:2800,seuilStockBas:10}),[b,S]=Dt.useState({impressionAutomatique:!1,taillePapier:"thermal"}),[D,f]=Dt.useState([]),[C,w]=Dt.useState(!1),[P,E]=Dt.useState(null),[U,F]=Dt.useState({nom:"",description:"",couleur:"#2196F3"}),k=is.getUserPermissions();Dt.useEffect(()=>{R()},[]);const R=async()=>{var e,n;const i=await ns.getSettings();t(i),g(i.entreprise),v({tauxChangeUSDCDF:i.tauxChangeUSDCDF,seuilStockBas:i.seuilStockBas}),S({impressionAutomatique:(null==(e=i.impression)?void 0:e.impressionAutomatique)||!1,taillePapier:(null==(n=i.impression)?void 0:n.taillePapier)||"thermal"}),f(i.categories)},M=e=>{e?(E(e),F({nom:e.nom,description:e.description,couleur:e.couleur})):(E(null),F({nom:"",description:"",couleur:"#2196F3"})),w(!0),u("")},N=()=>{w(!1),E(null),u("")};return e?i.jsxs(s,{children:[i.jsx(o,{variant:"h4",gutterBottom:!0,children:"Paramètres"}),a&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>l(""),children:a}),d&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>u(""),children:d}),i.jsx(c,{sx:{mb:3},children:i.jsxs(st,{value:n,onChange:(e,t)=>{r(t)},"aria-label":"settings tabs",children:[i.jsx(rt,{label:"Entreprise",icon:i.jsx(pt,{})}),i.jsx(rt,{label:"Général",icon:i.jsx(de,{})}),i.jsx(rt,{label:"Impression",icon:i.jsx(ze,{})}),i.jsx(rt,{label:"Catégories",icon:i.jsx(ue,{})}),i.jsx(rt,{label:"Sauvegarde",icon:i.jsx(dt,{})}),i.jsx(rt,{label:"Données CSV",icon:i.jsx(Fe,{})})]})}),i.jsx(Xs,{value:n,index:0,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Informations de l'Entreprise",avatar:i.jsx(pt,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Nom de l'entreprise",value:m.nom,onChange:e=>g({...m,nom:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Email",type:"email",value:m.email,onChange:e=>g({...m,email:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Téléphone",value:m.telephone,onChange:e=>g({...m,telephone:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Adresse",value:m.adresse,onChange:e=>g({...m,adresse:e.target.value}),disabled:!k.canManageSettings})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"RCCM",value:m.rccm||"",onChange:e=>g({...m,rccm:e.target.value}),disabled:!k.canManageSettings,helperText:"Registre de Commerce et du Crédit Mobilier"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"ID NAT",value:m.idNat||"",onChange:e=>g({...m,idNat:e.target.value}),disabled:!k.canManageSettings,helperText:"Identification Nationale"})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Ws,{value:m.logo||"",onChange:e=>g({...m,logo:e}),disabled:!k.canManageSettings})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(A,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,entreprise:m};await ns.setSettings(n),t(n),l("Paramètres de l'entreprise sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(Xs,{value:n,index:1,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Paramètres Généraux",avatar:i.jsx(de,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(Es,{label:"Taux de change (1 USD = ? CDF)",value:j.tauxChangeUSDCDF,onChange:e=>v({...j,tauxChangeUSDCDF:e}),min:1e3,max:1e4,step:10,exchangeRate:j.tauxChangeUSDCDF,disabled:!k.canManageSettings,showSlider:!0,allowUSDInput:!1,helperText:"Définit le taux de conversion entre USD et CDF"})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsx(ne,{fullWidth:!0,label:"Seuil de stock bas",type:"number",value:j.seuilStockBas,onChange:e=>v({...j,seuilStockBas:parseInt(e.target.value)||0}),disabled:!k.canManageSettings,helperText:"Alerte quand le stock est ≤ à cette valeur"})}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(A,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;if(j.tauxChangeUSDCDF<=0)return void u("Le taux de change doit être supérieur à 0");if(j.seuilStockBas<0)return void u("Le seuil de stock bas ne peut pas être négatif");const n={...e,tauxChangeUSDCDF:j.tauxChangeUSDCDF,seuilStockBas:j.seuilStockBas};await ns.setSettings(n),t(n),l("Paramètres généraux sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(Xs,{value:n,index:2,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Paramètres d'Impression",avatar:i.jsx(ze,{})}),i.jsx(te,{children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsxs(ce,{item:!0,xs:12,children:[i.jsx(Qe,{control:i.jsx(Ze,{checked:b.impressionAutomatique,onChange:e=>S({...b,impressionAutomatique:e.target.checked}),disabled:!k.canManageSettings}),label:"Impression automatique des reçus"}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense"})]}),i.jsxs(ce,{item:!0,xs:12,md:6,children:[i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Taille du papier"}),i.jsxs(Se,{value:b.taillePapier,label:"Taille du papier",onChange:e=>S({...b,taillePapier:e.target.value}),disabled:!k.canManageSettings,children:[i.jsx(J,{value:"thermal",children:"Reçu thermique (80mm)"}),i.jsx(J,{value:"a4",children:"A4"})]})]}),i.jsx(o,{variant:"body2",color:"text.secondary",sx:{mt:1},children:"Choisissez le format de papier pour l'impression des reçus"})]}),k.canManageSettings&&i.jsx(ce,{item:!0,xs:12,children:i.jsx(A,{variant:"contained",startIcon:i.jsx(xt,{}),onClick:async()=>{if(!e)return;const n={...e,impression:{impressionAutomatique:b.impressionAutomatique,taillePapier:b.taillePapier}};await ns.setSettings(n),t(n),l("Paramètres d'impression sauvegardés avec succès"),setTimeout(()=>l(""),3e3)},children:"Sauvegarder"})})]})})]})}),i.jsx(Xs,{value:n,index:3,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Gestion des Catégories",avatar:i.jsx(ue,{}),action:k.canManageSettings&&i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>M(),children:"Nouvelle Catégorie"})}),i.jsx(te,{children:i.jsx(p,{children:D.map((n,r)=>i.jsxs(Ct.Fragment,{children:[i.jsxs(x,{children:[i.jsx(s,{sx:{width:20,height:20,backgroundColor:n.couleur,borderRadius:1,mr:2}}),i.jsx(y,{primary:n.nom,secondary:n.description}),k.canManageSettings&&i.jsxs(V,{children:[i.jsx(T,{edge:"end",onClick:()=>M(n),sx:{mr:1},children:i.jsx(Me,{})}),i.jsx(T,{edge:"end",color:"error",onClick:()=>(async n=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${n.nom}" ?`)){if((await ns.getProducts()).some(e=>e.categorie===n.nom))return u("Cette catégorie est utilisée par des produits et ne peut pas être supprimée"),void setTimeout(()=>u(""),5e3);const i=D.filter(e=>e.id!==n.id);if(f(i),e){const n={...e,categories:i};await ns.setSettings(n),t(n)}l("Catégorie supprimée avec succès"),setTimeout(()=>l(""),3e3)}})(n),children:i.jsx(q,{})})]})]}),r<D.length-1&&i.jsx(h,{})]},n.id))})})]})}),i.jsx(Xs,{value:n,index:4,children:i.jsxs(ce,{container:!0,spacing:3,children:[i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Sauvegarde des Données",avatar:i.jsx(Fe,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde."}),i.jsx(A,{variant:"contained",startIcon:i.jsx(Fe,{}),onClick:async()=>{try{const{csvImportExportService:e}=await vi(async()=>{const{csvImportExportService:e}=await Promise.resolve().then(()=>_s);return{csvImportExportService:e}},void 0,import.meta.url),t=await e.exportAllData();if(t.success&&t.data){const e=new Blob([t.data],{type:"text/plain;charset=utf-8"}),n=URL.createObjectURL(e),i=document.createElement("a");i.href=n,i.download=`SmartBoutique_Backup_${(new Date).toISOString().split("T")[0]}.csv`,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(n),l("Sauvegarde CSV exportée avec succès"),setTimeout(()=>l(""),3e3)}else u(t.message||"Erreur lors de l'exportation"),setTimeout(()=>u(""),3e3)}catch(e){u("Erreur lors de l'exportation des données"),setTimeout(()=>u(""),3e3)}},fullWidth:!0,sx:{mt:2},children:"Exporter les Données"})]})]})}),i.jsx(ce,{item:!0,xs:12,md:6,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Restauration des Données",avatar:i.jsx(ke,{})}),i.jsxs(te,{children:[i.jsx(o,{variant:"body2",color:"text.secondary",gutterBottom:!0,children:"Importez un fichier de sauvegarde pour restaurer vos données."}),i.jsx("input",{accept:".json",style:{display:"none"},id:"import-file",type:"file",onChange:async e=>{var t;const n=null==(t=e.target.files)?void 0:t[0];if(!n)return;const i=new FileReader;i.onload=async e=>{var t;try{const n=JSON.parse(null==(t=e.target)?void 0:t.result);await ns.importData(n)?(R(),l("Données importées avec succès"),setTimeout(()=>l(""),3e3),setTimeout(()=>window.location.reload(),2e3)):(u("Erreur lors de l'importation des données"),setTimeout(()=>u(""),3e3))}catch(n){u("Fichier de sauvegarde invalide"),setTimeout(()=>u(""),3e3)}},i.readAsText(n),e.target.value=""}}),i.jsx("label",{htmlFor:"import-file",children:i.jsx(A,{variant:"outlined",component:"span",startIcon:i.jsx(ke,{}),fullWidth:!0,sx:{mt:2},children:"Importer les Données"})})]})]})}),is.hasRole(["super_admin"])&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(ee,{children:[i.jsx(me,{title:"Réinitialisation",avatar:i.jsx(gt,{})}),i.jsxs(te,{children:[i.jsx(O,{severity:"warning",sx:{mb:2},children:i.jsxs(o,{variant:"body2",children:[i.jsx("strong",{children:"Attention:"})," Cette action supprimera toutes les données et restaurera les paramètres par défaut. Cette action est irréversible."]})}),i.jsx(A,{variant:"outlined",color:"error",startIcon:i.jsx(gt,{}),onClick:async()=>{window.confirm("Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action est irréversible.")&&window.confirm("ATTENTION: Toutes les données seront perdues. Confirmez-vous ?")&&(await ns.clear(),await ns.initializeDefaultData(),l("Données réinitialisées avec succès"),setTimeout(()=>{window.location.reload()},2e3))},children:"Réinitialiser toutes les Données"})]})]})})]})}),i.jsx(Xs,{value:n,index:5,children:i.jsx($s,{onSuccess:e=>{l(e),setTimeout(()=>l(""),3e3)},onError:e=>{u(e),setTimeout(()=>u(""),5e3)}})}),i.jsxs(Ae,{open:C,onClose:N,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ne,{children:P?"Modifier la Catégorie":"Nouvelle Catégorie"}),i.jsxs(Le,{children:[d&&i.jsx(O,{severity:"error",sx:{mb:2},children:d}),i.jsxs(ce,{container:!0,spacing:2,sx:{mt:1},children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom de la catégorie *",value:U.nom,onChange:e=>F({...U,nom:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Description",multiline:!0,rows:2,value:U.description,onChange:e=>F({...U,description:e.target.value})})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Couleur",type:"color",value:U.couleur,onChange:e=>F({...U,couleur:e.target.value}),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(yt,{})})}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsxs(s,{display:"flex",alignItems:"center",gap:1,children:[i.jsx(o,{variant:"body2",children:"Aperçu:"}),i.jsx(I,{label:U.nom||"Nom de la catégorie",sx:{backgroundColor:U.couleur,color:"white"}})]})})]})]}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:N,children:"Annuler"}),i.jsx(A,{onClick:async()=>{if(!U.nom.trim())return void u("Le nom de la catégorie est requis");if(D.some(e=>e.nom.toLowerCase()===U.nom.trim().toLowerCase()&&e.id!==(null==P?void 0:P.id)))return void u("Une catégorie avec ce nom existe déjà");let n;if(P)n=D.map(e=>e.id===P.id?{...e,nom:U.nom.trim(),description:U.description.trim(),couleur:U.couleur}:e);else{const e={id:Date.now().toString(),nom:U.nom.trim(),description:U.description.trim(),couleur:U.couleur};n=[...D,e]}if(f(n),e){const i={...e,categories:n};await ns.setSettings(i),t(i)}l(P?"Catégorie mise à jour":"Catégorie créée"),setTimeout(()=>l(""),3e3),N()},variant:"contained",children:P?"Mettre à jour":"Créer"})]})]})]}):i.jsx(o,{children:"Chargement..."})},Js=()=>{const[e,t]=Dt.useState([]),[n,r]=Dt.useState([]),[a,l]=Dt.useState(""),[d,u]=Dt.useState(""),[m,h]=Dt.useState(""),[p,x]=Dt.useState(0),[g,y]=Dt.useState(10),[j,v]=Dt.useState(!1),[b,S]=Dt.useState(null),[D,f]=Dt.useState({nomEmploye:"",montantCDF:0,datePaiement:Tt(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""}),[C,w]=Dt.useState(""),[P,E]=Dt.useState(""),[F,k]=Dt.useState({tauxChangeUSDCDF:2800}),[R,M]=Dt.useState(!1),N=is.getUserPermissions(),L=is.getCurrentUser(),V=[{value:"cash",label:"Comptant"},{value:"mobile_money",label:"Mobile Money"},{value:"bank",label:"Banque"}];Dt.useEffect(()=>{B(),z()},[]),Dt.useEffect(()=>{_()},[e,a,d,m]);const B=async()=>{try{M(!0);const e=await ns.getEmployeePayments();t(e)}catch(e){console.error("Error loading employee payments:",e),w("Erreur lors du chargement des paiements employés")}finally{M(!1)}},z=async()=>{try{const e=await ns.getSettings();k(e)}catch(e){console.error("Error loading settings:",e)}},_=()=>{let t=[...e];if(a&&(t=t.filter(e=>{var t;return e.nomEmploye.toLowerCase().includes(a.toLowerCase())||(null==(t=e.notes)?void 0:t.toLowerCase().includes(a.toLowerCase()))})),d&&(t=t.filter(e=>e.methodePaiement===d)),m){const e=new Date;let n,i=e;switch(m){case"today":n=new Date(e.getFullYear(),e.getMonth(),e.getDate());break;case"week":n=new Date(e.getTime()-6048e5);break;case"month":n=At(e),i=Nt(e);break;case"year":n=new Date(e.getFullYear(),0,1);break;default:n=new Date(0)}t=t.filter(e=>{const t=new Date(e.datePaiement);return Ot(t,{start:n,end:i})})}r(t),x(0)},$=e=>{e?(S(e),f({nomEmploye:e.nomEmploye,montantCDF:e.montantCDF,datePaiement:e.datePaiement,methodePaiement:e.methodePaiement,notes:e.notes||""})):(S(null),f({nomEmploye:"",montantCDF:0,datePaiement:Tt(new Date,"yyyy-MM-dd"),methodePaiement:"cash",notes:""})),v(!0),w(""),E("")},W=()=>{v(!1),S(null),w(""),E("")},X=e=>{const t=V.find(t=>t.value===e);return t?t.label:e},Q=n.reduce((e,t)=>e+t.montantCDF,0),H=n.reduce((e,t)=>e+(t.montantUSD||0),0);return i.jsxs(s,{p:3,children:[i.jsxs(o,{variant:"h4",gutterBottom:!0,children:[i.jsx(Y,{sx:{mr:1,verticalAlign:"middle"}}),"Paiements Employés"]}),i.jsxs(ce,{container:!0,spacing:3,sx:{mb:3},children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Total Paiements"}),i.jsx(o,{variant:"h5",children:n.length})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total (CDF)"}),i.jsx(o,{variant:"h5",children:Ss(Q,"CDF")})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Montant Total (USD)"}),i.jsx(o,{variant:"h5",children:Ss(H,"USD")})]})})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ee,{children:i.jsxs(te,{children:[i.jsx(o,{color:"textSecondary",gutterBottom:!0,children:"Employés Payés"}),i.jsx(o,{variant:"h5",children:new Set(n.map(e=>e.nomEmploye)).size})]})})})]}),C&&i.jsx(O,{severity:"error",sx:{mb:2},onClose:()=>w(""),children:C}),P&&i.jsx(O,{severity:"success",sx:{mb:2},onClose:()=>E(""),children:P}),i.jsx(c,{sx:{p:2,mb:3},children:i.jsxs(ce,{container:!0,spacing:2,alignItems:"center",children:[i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(ne,{fullWidth:!0,label:"Rechercher employé",value:a,onChange:e=>l(e.target.value),InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(Te,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Méthode de paiement"}),i.jsxs(Se,{value:d,onChange:e=>u(e.target.value),label:"Méthode de paiement",children:[i.jsx(J,{value:"",children:"Toutes"}),V.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))]})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Période"}),i.jsx(Se,{value:m,onChange:e=>h(e.target.value),label:"Période",children:[{value:"",label:"Toutes les dates"},{value:"today",label:"Aujourd'hui"},{value:"week",label:"Cette semaine"},{value:"month",label:"Ce mois"},{value:"year",label:"Cette année"}].map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:3,children:i.jsx(A,{variant:"contained",startIcon:i.jsx(Ue,{}),onClick:()=>$(),disabled:!N.canManageEmployeePayments||R,fullWidth:!0,children:"Nouveau Paiement"})}),i.jsx(ce,{item:!0,xs:12,sm:6,md:2,children:i.jsx(A,{variant:"outlined",onClick:()=>{l(""),u(""),h("")},fullWidth:!0,children:"Réinitialiser"})})]})}),i.jsxs(c,{children:[i.jsx(he,{children:i.jsxs(pe,{children:[i.jsx(xe,{children:i.jsxs(ge,{children:[i.jsx(ye,{children:"Employé"}),i.jsx(ye,{children:"Montant (CDF)"}),i.jsx(ye,{children:"Montant (USD)"}),i.jsx(ye,{children:"Date de Paiement"}),i.jsx(ye,{children:"Méthode"}),i.jsx(ye,{children:"Notes"}),i.jsx(ye,{children:"Créé par"}),N.canManageEmployeePayments&&i.jsx(ye,{children:"Actions"})]})}),i.jsx(je,{children:R?i.jsx(ge,{children:i.jsx(ye,{colSpan:8,align:"center",children:i.jsx(Be,{})})}):0===n.length?i.jsx(ge,{children:i.jsx(ye,{colSpan:8,align:"center",children:i.jsx(o,{variant:"body2",color:"textSecondary",children:"Aucun paiement employé trouvé"})})}):n.slice(p*g,p*g+g).map(e=>i.jsxs(ge,{children:[i.jsx(ye,{children:i.jsxs(s,{display:"flex",alignItems:"center",children:[i.jsx(lt,{sx:{mr:1,color:"primary.main"}}),e.nomEmploye]})}),i.jsx(ye,{children:i.jsx(o,{variant:"body2",fontWeight:"bold",color:"primary",children:Ss(e.montantCDF,"CDF")})}),i.jsx(ye,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:Ss(e.montantUSD||0,"USD")})}),i.jsx(ye,{children:i.jsxs(s,{display:"flex",alignItems:"center",children:[i.jsx(it,{sx:{mr:1,fontSize:16,color:"text.secondary"}}),Tt(new Date(e.datePaiement),"dd/MM/yyyy",{locale:cs})]})}),i.jsx(ye,{children:i.jsx(I,{label:X(e.methodePaiement),size:"small",color:"cash"===e.methodePaiement?"success":"mobile_money"===e.methodePaiement?"info":"default"})}),i.jsx(ye,{children:i.jsx(o,{variant:"body2",noWrap:!0,sx:{maxWidth:150},children:e.notes||"-"})}),i.jsx(ye,{children:i.jsx(o,{variant:"body2",color:"textSecondary",children:e.creePar})}),N.canManageEmployeePayments&&i.jsxs(ye,{children:[i.jsx(U,{title:"Modifier",children:i.jsx(T,{size:"small",onClick:()=>$(e),disabled:R,children:i.jsx(Me,{})})}),i.jsx(U,{title:"Supprimer",children:i.jsx(T,{size:"small",onClick:()=>(async e=>{if(window.confirm(`Êtes-vous sûr de vouloir supprimer le paiement de ${e.nomEmploye} ?`))try{M(!0),await ns.deleteEmployeePayment(e.id),E("Paiement employé supprimé avec succès"),await B()}catch(t){console.error("Error deleting employee payment:",t),w("Erreur lors de la suppression du paiement employé")}finally{M(!1)}})(e),disabled:R,color:"error",children:i.jsx(q,{})})})]})]},e.id))})]})}),i.jsx(Ie,{rowsPerPageOptions:[5,10,25,50,100],component:"div",count:n.length,rowsPerPage:g,page:p,onPageChange:(e,t)=>{x(t)},onRowsPerPageChange:e=>{y(parseInt(e.target.value,10)),x(0)},labelRowsPerPage:"Lignes par page:",labelDisplayedRows:({from:e,to:t,count:n})=>`${e}-${t} sur ${-1!==n?n:`plus de ${t}`}`})]}),i.jsxs(Ae,{open:j,onClose:W,maxWidth:"sm",fullWidth:!0,children:[i.jsx(Ne,{children:b?"Modifier le Paiement Employé":"Nouveau Paiement Employé"}),i.jsx(Le,{children:i.jsx(s,{sx:{pt:1},children:i.jsxs(ce,{container:!0,spacing:2,children:[i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,label:"Nom de l'employé *",value:D.nomEmploye,onChange:e=>f({...D,nomEmploye:e.target.value}),disabled:R,InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(lt,{})})}})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(Es,{label:"Montant du paiement *",value:D.montantCDF,onChange:e=>f({...D,montantCDF:e}),currency:"CDF",disabled:R,fullWidth:!0})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsx(ne,{fullWidth:!0,type:"date",label:"Date de paiement *",value:D.datePaiement,onChange:e=>f({...D,datePaiement:e.target.value}),disabled:R,InputLabelProps:{shrink:!0},InputProps:{startAdornment:i.jsx(ie,{position:"start",children:i.jsx(it,{})})}})}),i.jsx(ce,{item:!0,xs:12,sm:6,children:i.jsxs(ve,{fullWidth:!0,children:[i.jsx(be,{children:"Méthode de paiement *"}),i.jsx(Se,{value:D.methodePaiement,onChange:e=>f({...D,methodePaiement:e.target.value}),label:"Méthode de paiement *",disabled:R,children:V.map(e=>i.jsx(J,{value:e.value,children:e.label},e.value))})]})}),i.jsx(ce,{item:!0,xs:12,children:i.jsx(ne,{fullWidth:!0,multiline:!0,rows:3,label:"Notes (optionnel)",value:D.notes,onChange:e=>f({...D,notes:e.target.value}),disabled:R,placeholder:"Ajoutez des notes sur ce paiement..."})}),D.montantCDF>0&&i.jsx(ce,{item:!0,xs:12,children:i.jsxs(s,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[i.jsxs(o,{variant:"body2",color:"textSecondary",gutterBottom:!0,children:["Équivalent USD (taux: ",F.tauxChangeUSDCDF," CDF/USD)"]}),i.jsx(o,{variant:"h6",color:"primary",children:Ss(D.montantCDF/F.tauxChangeUSDCDF,"USD")})]})})]})})}),i.jsxs(Oe,{children:[i.jsx(A,{onClick:W,disabled:R,children:"Annuler"}),i.jsx(A,{onClick:async()=>{var e;try{if(M(!0),w(""),!D.nomEmploye.trim())return void w("Le nom de l'employé est requis");if(D.montantCDF<=0)return void w("Le montant doit être supérieur à zéro");const t={id:(null==b?void 0:b.id)||`emp_pay_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,nomEmploye:D.nomEmploye.trim(),montantCDF:D.montantCDF,montantUSD:D.montantCDF/F.tauxChangeUSDCDF,datePaiement:D.datePaiement,methodePaiement:D.methodePaiement,notes:null==(e=D.notes)?void 0:e.trim(),creePar:(null==L?void 0:L.nom)||"Système",dateCreation:(null==b?void 0:b.dateCreation)||(new Date).toISOString(),dateModification:b?(new Date).toISOString():void 0};b?(await ns.updateEmployeePayment(t),E("Paiement employé modifié avec succès")):(await ns.addEmployeePayment(t),E("Paiement employé ajouté avec succès")),await B(),W()}catch(t){console.error("Error saving employee payment:",t),w("Erreur lors de la sauvegarde du paiement employé")}finally{M(!1)}},variant:"contained",disabled:R||!D.nomEmploye.trim()||D.montantCDF<=0,startIcon:R?i.jsx(Be,{size:20}):i.jsx(Y,{}),children:b?"Modifier":"Ajouter"})]})]})]})},Hs=()=>{var e,t,n,i,s,r,a,o,l;const c=(()=>{const e=Ri();return{components:{MuiButton:{styleOverrides:{root:{minHeight:e.isMobile?48:36,fontSize:e.isMobile?"1rem":"0.875rem"}}},MuiIconButton:{styleOverrides:{root:{padding:e.isMobile?12:8}}},MuiTableCell:{styleOverrides:{root:{padding:e.isMobile?"12px 8px":"16px"}}}}}})();return bt({palette:{primary:{main:"#1976d2"},secondary:{main:"#dc004e"},background:{default:"#f5f5f5"}},typography:{fontFamily:'"Roboto", "Helvetica", "Arial", sans-serif',h4:{fontWeight:600},h5:{fontWeight:600},h6:{fontWeight:600}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",...null==(n=null==(t=null==(e=c.components)?void 0:e.MuiButton)?void 0:t.styleOverrides)?void 0:n.root}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 8px rgba(0,0,0,0.1)"}}},MuiIconButton:{styleOverrides:{root:{...null==(r=null==(s=null==(i=c.components)?void 0:i.MuiIconButton)?void 0:s.styleOverrides)?void 0:r.root}}},MuiTableCell:{styleOverrides:{root:{...null==(l=null==(o=null==(a=c.components)?void 0:a.MuiTableCell)?void 0:o.styleOverrides)?void 0:l.root}}}}},yi)},Ys=()=>{const[e,t]=Dt.useState(null),[n,r]=Dt.useState(!0),[a,o]=Dt.useState(null),[l,c]=Dt.useState(()=>Hs());Dt.useEffect(()=>{const e=()=>{document.querySelectorAll("input, textarea, [contenteditable]").forEach(e=>{const t=e;t.style.pointerEvents="auto",t.style.userSelect="text",(!t.hasAttribute("tabindex")||t.tabIndex<0)&&(t.tabIndex=0)})};e();const t=()=>{setTimeout(e,50)};return window.addEventListener("hashchange",t),()=>{window.removeEventListener("hashchange",t)}},[]),Dt.useEffect(()=>{(async()=>{try{0,await ns.initializeDefaultData();Ri().isMobile&&await ns.migrateFromDesktop(),await is.initialize();const e=is.getCurrentUser();t(e),r(!1)}catch(e){console.error("SmartBoutique: Error during initialization:",e),o(e instanceof Error?e.message:"Unknown error"),r(!1)}})()},[]);return a?i.jsxs(jt,{theme:l,children:[i.jsx(vt,{}),i.jsxs(s,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,children:[i.jsx("h2",{children:"Erreur de chargement"}),i.jsx("p",{children:"Une erreur s'est produite lors du chargement de l'application:"}),i.jsx("pre",{style:{background:"#f5f5f5",padding:"10px",borderRadius:"4px"},children:a}),i.jsx("button",{onClick:()=>window.location.reload(),children:"Recharger l'application"})]})]}):n?i.jsxs(jt,{theme:l,children:[i.jsx(vt,{}),i.jsx(s,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"100vh",children:"Chargement..."})]}):i.jsxs(jt,{theme:l,children:[i.jsx(vt,{}),i.jsx(mi,{children:i.jsxs(ci,{children:[i.jsx(oi,{path:"/login",element:e?i.jsx(ri,{to:"/dashboard",replace:!0}):i.jsx(ys,{onLogin:e=>{t(e)}})}),i.jsxs(oi,{path:"/",element:i.jsx(xs,{children:i.jsx(ps,{currentUser:e,onLogout:async()=>{await is.logout(),t(null)}})}),children:[i.jsx(oi,{index:!0,element:i.jsx(ri,{to:"/dashboard",replace:!0})}),i.jsx(oi,{path:"dashboard",element:i.jsx(xs,{requiredPermission:"canViewDashboard",children:i.jsx(bs,{})})}),i.jsx(oi,{path:"products",element:i.jsx(xs,{requiredPermission:"canViewProducts",children:i.jsx(Ts,{})})}),i.jsx(oi,{path:"sales",element:i.jsx(xs,{requiredPermission:"canViewSales",children:i.jsx(Is,{})})}),i.jsx(oi,{path:"debts",element:i.jsx(xs,{requiredPermission:"canViewDebts",children:i.jsx(As,{})})}),i.jsx(oi,{path:"expenses",element:i.jsx(xs,{requiredPermission:"canViewExpenses",children:i.jsx(Ls,{})})}),i.jsx(oi,{path:"employee-payments",element:i.jsx(xs,{requiredPermission:"canViewEmployeePayments",children:i.jsx(Js,{})})}),i.jsx(oi,{path:"reports",element:i.jsx(xs,{requiredPermission:"canViewReports",children:i.jsx(Vs,{})})}),i.jsx(oi,{path:"users",element:i.jsx(xs,{requiredPermission:"canViewUsers",children:i.jsx(qs,{})})}),i.jsx(oi,{path:"settings",element:i.jsx(xs,{requiredPermission:"canViewSettings",children:i.jsx(Qs,{})})})]}),i.jsx(oi,{path:"*",element:i.jsx(ri,{to:"/dashboard",replace:!0})})]})})]})};class Ks extends Dt.Component{constructor(e){super(e),n(this,"handleReload",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0}),window.location.reload()}),n(this,"handleReset",()=>{this.setState({hasError:!1,error:void 0,errorInfo:void 0})}),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("ErrorBoundary caught an error:",e,t),this.setState({error:e,errorInfo:t})}render(){return this.state.hasError?i.jsx(s,{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",minHeight:"100vh",p:3,bgcolor:"#f5f5f5",children:i.jsxs(c,{elevation:3,sx:{p:4,maxWidth:600,width:"100%"},children:[i.jsx(o,{variant:"h4",color:"error",gutterBottom:!0,children:"Oops! Une erreur s'est produite"}),i.jsx(o,{variant:"body1",paragraph:!0,children:"L'application a rencontré une erreur inattendue. Vous pouvez essayer de continuer ou recharger l'application."}),!1,i.jsxs(s,{display:"flex",gap:2,mt:3,children:[i.jsx(A,{variant:"contained",color:"primary",onClick:this.handleReset,children:"Continuer"}),i.jsx(A,{variant:"outlined",color:"secondary",onClick:this.handleReload,children:"Recharger l'application"})]})]})}):this.props.children}}zt.register(_t,$t,Wt,Xt,Qt,Jt,Ht,Yt,Kt);const Gs=document.getElementById("root");Gs?en.createRoot(Gs).render(i.jsx(Ct.StrictMode,{children:i.jsx(Ks,{children:i.jsx(Ys,{})})})):console.error("SmartBoutique: Root element not found!");export{Pi as W};
