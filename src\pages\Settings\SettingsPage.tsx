import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  TextField,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Alert,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,

  Chip,
  InputAdornment,
  Tab,
  Tabs,
  Switch,
  FormControlLabel,

} from '@mui/material';
import {
  Save,
  Add,
  Edit,
  Delete,
  Download,
  Upload,

  Business,
  AttachMoney,
  Category,
  Backup,
  RestoreFromTrash,
  ColorLens,
  Print,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';

// Components
import { CurrencyInput } from '@/components/CurrencyInput';
import { CSVManager } from '@/components/CSVManager';
import { LogoUpload } from '@/components/Common';

// Types
import { Settings, Category as CategoryType } from '@/types';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function CustomTabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SettingsPage: React.FC = () => {
  const [settings, setSettings] = useState<Settings | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');

  // Company settings
  const [companyData, setCompanyData] = useState({
    nom: '',
    adresse: '',
    telephone: '',
    email: '',
    rccm: '', // Registre de Commerce et du Crédit Mobilier
    idNat: '', // Identification Nationale
    logo: '', // Base64 encoded logo image data
  });

  // General settings
  const [generalData, setGeneralData] = useState({
    tauxChangeUSDCDF: 2800,
    seuilStockBas: 10,
  });

  // Receipt printing settings
  const [printingData, setPrintingData] = useState({
    impressionAutomatique: false,
    taillePapier: 'thermal' as 'thermal' | 'a4',
  });

  // Category management
  const [categories, setCategories] = useState<CategoryType[]>([]);
  const [openCategoryDialog, setOpenCategoryDialog] = useState(false);
  const [editingCategory, setEditingCategory] = useState<CategoryType | null>(null);
  const [categoryForm, setCategoryForm] = useState({
    nom: '',
    description: '',
    couleur: '#2196F3',
  });

  const permissions = adaptiveAuthService.getUserPermissions();

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    const settingsData = await adaptiveStorageService.getSettings();
    setSettings(settingsData);
    setCompanyData(settingsData.entreprise);
    setGeneralData({
      tauxChangeUSDCDF: settingsData.tauxChangeUSDCDF,
      seuilStockBas: settingsData.seuilStockBas,
    });
    setPrintingData({
      impressionAutomatique: settingsData.impression?.impressionAutomatique || false,
      taillePapier: settingsData.impression?.taillePapier || 'thermal',
    });
    setCategories(settingsData.categories);
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSaveCompanySettings = async () => {
    if (!settings) return;

    const updatedSettings = {
      ...settings,
      entreprise: companyData,
    };

    await adaptiveStorageService.setSettings(updatedSettings);
    setSettings(updatedSettings);
    setSuccess('Paramètres de l\'entreprise sauvegardés avec succès');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleSaveGeneralSettings = async () => {
    if (!settings) return;

    if (generalData.tauxChangeUSDCDF <= 0) {
      setError('Le taux de change doit être supérieur à 0');
      return;
    }

    if (generalData.seuilStockBas < 0) {
      setError('Le seuil de stock bas ne peut pas être négatif');
      return;
    }

    const updatedSettings = {
      ...settings,
      tauxChangeUSDCDF: generalData.tauxChangeUSDCDF,
      seuilStockBas: generalData.seuilStockBas,
    };

    await adaptiveStorageService.setSettings(updatedSettings);
    setSettings(updatedSettings);
    setSuccess('Paramètres généraux sauvegardés avec succès');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleSavePrintingSettings = async () => {
    if (!settings) return;

    const updatedSettings = {
      ...settings,
      impression: {
        impressionAutomatique: printingData.impressionAutomatique,
        taillePapier: printingData.taillePapier,
      },
    };

    await adaptiveStorageService.setSettings(updatedSettings);
    setSettings(updatedSettings);
    setSuccess('Paramètres d\'impression sauvegardés avec succès');
    setTimeout(() => setSuccess(''), 3000);
  };

  const handleOpenCategoryDialog = (category?: CategoryType) => {
    if (category) {
      setEditingCategory(category);
      setCategoryForm({
        nom: category.nom,
        description: category.description,
        couleur: category.couleur,
      });
    } else {
      setEditingCategory(null);
      setCategoryForm({
        nom: '',
        description: '',
        couleur: '#2196F3',
      });
    }
    setOpenCategoryDialog(true);
    setError('');
  };

  const handleCloseCategoryDialog = () => {
    setOpenCategoryDialog(false);
    setEditingCategory(null);
    setError('');
  };

  const handleSaveCategory = async () => {
    if (!categoryForm.nom.trim()) {
      setError('Le nom de la catégorie est requis');
      return;
    }

    // Check for duplicate names (excluding current category if editing)
    const isDuplicate = categories.some(cat =>
      cat.nom.toLowerCase() === categoryForm.nom.trim().toLowerCase() &&
      cat.id !== editingCategory?.id
    );

    if (isDuplicate) {
      setError('Une catégorie avec ce nom existe déjà');
      return;
    }

    let updatedCategories: CategoryType[];

    if (editingCategory) {
      // Update existing category
      updatedCategories = categories.map(cat =>
        cat.id === editingCategory.id
          ? {
              ...cat,
              nom: categoryForm.nom.trim(),
              description: categoryForm.description.trim(),
              couleur: categoryForm.couleur,
            }
          : cat
      );
    } else {
      // Add new category
      const newCategory: CategoryType = {
        id: Date.now().toString(),
        nom: categoryForm.nom.trim(),
        description: categoryForm.description.trim(),
        couleur: categoryForm.couleur,
      };
      updatedCategories = [...categories, newCategory];
    }

    setCategories(updatedCategories);

    // Update settings
    if (settings) {
      const updatedSettings = {
        ...settings,
        categories: updatedCategories,
      };
      await adaptiveStorageService.setSettings(updatedSettings);
      setSettings(updatedSettings);
    }

    setSuccess(editingCategory ? 'Catégorie mise à jour' : 'Catégorie créée');
    setTimeout(() => setSuccess(''), 3000);
    handleCloseCategoryDialog();
  };

  const handleDeleteCategory = async (category: CategoryType) => {
    if (window.confirm(`Êtes-vous sûr de vouloir supprimer la catégorie "${category.nom}" ?`)) {
      // Check if category is used by products
      const products = await adaptiveStorageService.getProducts();
      const isUsed = products.some((product: any) => product.categorie === category.nom);

      if (isUsed) {
        setError('Cette catégorie est utilisée par des produits et ne peut pas être supprimée');
        setTimeout(() => setError(''), 5000);
        return;
      }

      const updatedCategories = categories.filter(cat => cat.id !== category.id);
      setCategories(updatedCategories);

      if (settings) {
        const updatedSettings = {
          ...settings,
          categories: updatedCategories,
        };
        await adaptiveStorageService.setSettings(updatedSettings);
        setSettings(updatedSettings);
      }

      setSuccess('Catégorie supprimée avec succès');
      setTimeout(() => setSuccess(''), 3000);
    }
  };

  const handleExportData = async () => {
    try {
      // Use CSV export for unified format
      const { csvImportExportService } = await import('@/services/csv-import-export');
      const result = await csvImportExportService.exportAllData();

      if (result.success && result.data) {
        const blob = new Blob([result.data], { type: 'text/plain;charset=utf-8' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `SmartBoutique_Backup_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        setSuccess('Sauvegarde CSV exportée avec succès');
        setTimeout(() => setSuccess(''), 3000);
      } else {
        setError(result.message || 'Erreur lors de l\'exportation');
        setTimeout(() => setError(''), 3000);
      }
    } catch (err) {
      setError('Erreur lors de l\'exportation des données');
      setTimeout(() => setError(''), 3000);
    }
  };

  const handleImportData = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = async (e) => {
      try {
        const data = JSON.parse(e.target?.result as string);
        const success = await adaptiveStorageService.importData(data);

        if (success) {
          loadSettings();
          setSuccess('Données importées avec succès');
          setTimeout(() => setSuccess(''), 3000);
          // Reload page to reflect changes
          setTimeout(() => window.location.reload(), 2000);
        } else {
          setError('Erreur lors de l\'importation des données');
          setTimeout(() => setError(''), 3000);
        }
      } catch (err) {
        setError('Fichier de sauvegarde invalide');
        setTimeout(() => setError(''), 3000);
      }
    };
    reader.readAsText(file);

    // Reset input
    event.target.value = '';
  };

  const handleResetData = async () => {
    // First confirmation dialog
    if (!window.confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données ? Cette action supprimera définitivement :')) {
      return;
    }

    // Second confirmation with detailed warning
    const confirmMessage = `ATTENTION: Cette action va supprimer TOUTES les données suivantes :

• Tous les produits et inventaire
• Toutes les ventes et transactions
• Toutes les dettes et paiements
• Tous les employés et leurs paiements
• Toutes les dépenses
• Tous les utilisateurs (sauf admin système)
• Tous les fichiers CSV et données importées

L'application sera restaurée avec des données de démonstration fraîches.

Cette action est IRRÉVERSIBLE. Confirmez-vous ?`;

    if (!window.confirm(confirmMessage)) {
      return;
    }

    try {
      // Show loading state
      setError('');
      setSuccess('Réinitialisation en cours... Veuillez patienter.');

      // Get current user info to preserve login
      const currentUser = await adaptiveStorageService.getCurrentUser();
      const userEmail = currentUser?.email || '<EMAIL>';

      // Comprehensive data reset
      await performCompleteDataReset();

      // Reinitialize with fresh demo data
      await adaptiveStorageService.initializeDefaultData();

      // Re-login the user with fresh data (ensures proper permissions)
      const users = await adaptiveStorageService.getUsers();
      const adminUser = users.find(u => u.email === userEmail) || users.find(u => u.role === 'super_admin');

      if (adminUser) {
        await adaptiveStorageService.setCurrentUser(adminUser);
      }

      // Show success message
      setSuccess('✅ Données réinitialisées avec succès ! Redirection vers le tableau de bord...');

      // Reload the page to ensure proper authentication state
      setTimeout(() => {
        window.location.reload();
      }, 2000);

    } catch (error) {
      console.error('Erreur lors de la réinitialisation:', error);
      setError('Erreur lors de la réinitialisation des données. Veuillez réessayer.');
      setTimeout(() => setError(''), 5000);
    }
  };

  /**
   * Perform comprehensive data reset across all storage systems
   */
  const performCompleteDataReset = async () => {
    try {
      // Detect platform and clear data accordingly
      const isMobile = () => {
        return window.location.protocol === 'capacitor:' ||
               (window as any).Capacitor?.isNativePlatform?.() ||
               navigator.userAgent.includes('Capacitor');
      };

      if (isMobile()) {
        // Mobile platform - clear CSV and SQLite data
        await clearMobileData();
      } else {
        // Desktop platform - clear localStorage and SQLite data
        await clearDesktopData();
      }

      console.log('✅ Complete data reset performed successfully');
    } catch (error) {
      console.error('❌ Error during data reset:', error);
      throw error;
    }
  };

  /**
   * Clear all mobile data (CSV Preferences + SQLite)
   */
  const clearMobileData = async () => {
    try {
      // Import mobile storage services
      const { Preferences } = await import('@capacitor/preferences');

      // Clear all SmartBoutique preferences
      const { keys } = await Preferences.keys();
      const smartBoutiqueKeys = keys.filter(key =>
        key.startsWith('smartboutique_') ||
        key.startsWith('smartboutique_csv_')
      );

      for (const key of smartBoutiqueKeys) {
        await Preferences.remove({ key });
      }

      // Clear SQLite database if available
      try {
        const { mobileSQLiteStorageService } = await import('@/services/mobile-sqlite-storage');
        // Note: SQLite clearing would need to be implemented in the service
        console.log('Mobile SQLite data cleared (if available)');
      } catch (error) {
        console.log('Mobile SQLite not available or already cleared');
      }

      console.log('✅ Mobile data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing mobile data:', error);
      throw error;
    }
  };

  /**
   * Clear all desktop data (localStorage + SQLite)
   */
  const clearDesktopData = async () => {
    try {
      // Clear all localStorage data with SmartBoutique prefix
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('smartboutique_')) {
          localStorage.removeItem(key);
        }
      });

      // Clear SQLite database if available
      try {
        const { sqliteStorageService } = await import('@/services/sqlite-storage');
        // Note: SQLite clearing would need to be implemented in the service
        console.log('Desktop SQLite data cleared (if available)');
      } catch (error) {
        console.log('Desktop SQLite not available or already cleared');
      }

      console.log('✅ Desktop data cleared successfully');
    } catch (error) {
      console.error('❌ Error clearing desktop data:', error);
      throw error;
    }
  };

  if (!settings) {
    return <Typography>Chargement...</Typography>;
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Paramètres
      </Typography>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError('')}>
          {error}
        </Alert>
      )}

      {/* Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs value={tabValue} onChange={handleTabChange} aria-label="settings tabs">
          <Tab label="Entreprise" icon={<Business />} />
          <Tab label="Général" icon={<AttachMoney />} />
          <Tab label="Impression" icon={<Print />} />
          <Tab label="Catégories" icon={<Category />} />
          <Tab label="Sauvegarde" icon={<Backup />} />
          <Tab label="Données CSV" icon={<Download />} />
        </Tabs>
      </Paper>

      {/* Company Settings Tab */}
      <CustomTabPanel value={tabValue} index={0}>
        <Card>
          <CardHeader
            title="Informations de l'Entreprise"
            avatar={<Business />}
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Nom de l'entreprise"
                  value={companyData.nom}
                  onChange={(e) => setCompanyData({ ...companyData, nom: e.target.value })}
                  disabled={!permissions.canManageSettings}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Email"
                  type="email"
                  value={companyData.email}
                  onChange={(e) => setCompanyData({ ...companyData, email: e.target.value })}
                  disabled={!permissions.canManageSettings}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Téléphone"
                  value={companyData.telephone}
                  onChange={(e) => setCompanyData({ ...companyData, telephone: e.target.value })}
                  disabled={!permissions.canManageSettings}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Adresse"
                  value={companyData.adresse}
                  onChange={(e) => setCompanyData({ ...companyData, adresse: e.target.value })}
                  disabled={!permissions.canManageSettings}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="RCCM"
                  value={companyData.rccm || ''}
                  onChange={(e) => setCompanyData({ ...companyData, rccm: e.target.value })}
                  disabled={!permissions.canManageSettings}
                  helperText="Registre de Commerce et du Crédit Mobilier"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="ID NAT"
                  value={companyData.idNat || ''}
                  onChange={(e) => setCompanyData({ ...companyData, idNat: e.target.value })}
                  disabled={!permissions.canManageSettings}
                  helperText="Identification Nationale"
                />
              </Grid>
              <Grid item xs={12}>
                <LogoUpload
                  value={companyData.logo || ''}
                  onChange={(logoData) => setCompanyData({ ...companyData, logo: logoData })}
                  disabled={!permissions.canManageSettings}
                />
              </Grid>
              {permissions.canManageSettings && (
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSaveCompanySettings}
                  >
                    Sauvegarder
                  </Button>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </CustomTabPanel>

      {/* General Settings Tab */}
      <CustomTabPanel value={tabValue} index={1}>
        <Card>
          <CardHeader
            title="Paramètres Généraux"
            avatar={<AttachMoney />}
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <CurrencyInput
                  label="Taux de change (1 USD = ? CDF)"
                  value={generalData.tauxChangeUSDCDF}
                  onChange={(value) => setGeneralData({
                    ...generalData,
                    tauxChangeUSDCDF: value
                  })}
                  min={1000}
                  max={10000}
                  step={10}
                  exchangeRate={generalData.tauxChangeUSDCDF}
                  disabled={!permissions.canManageSettings}
                  showSlider={true}
                  allowUSDInput={false}
                  helperText="Définit le taux de conversion entre USD et CDF"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Seuil de stock bas"
                  type="number"
                  value={generalData.seuilStockBas}
                  onChange={(e) => setGeneralData({
                    ...generalData,
                    seuilStockBas: parseInt(e.target.value) || 0
                  })}
                  disabled={!permissions.canManageSettings}
                  helperText="Alerte quand le stock est ≤ à cette valeur"
                />
              </Grid>
              {permissions.canManageSettings && (
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSaveGeneralSettings}
                  >
                    Sauvegarder
                  </Button>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </CustomTabPanel>

      {/* Printing Settings Tab */}
      <CustomTabPanel value={tabValue} index={2}>
        <Card>
          <CardHeader
            title="Paramètres d'Impression"
            avatar={<Print />}
          />
          <CardContent>
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={printingData.impressionAutomatique}
                      onChange={(e) => setPrintingData({
                        ...printingData,
                        impressionAutomatique: e.target.checked
                      })}
                      disabled={!permissions.canManageSettings}
                    />
                  }
                  label="Impression automatique des reçus"
                />
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Lorsque activé, les reçus seront automatiquement imprimés après chaque vente ou dépense
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <FormControl fullWidth>
                  <InputLabel>Taille du papier</InputLabel>
                  <Select
                    value={printingData.taillePapier}
                    label="Taille du papier"
                    onChange={(e) => setPrintingData({
                      ...printingData,
                      taillePapier: e.target.value as 'thermal' | 'a4'
                    })}
                    disabled={!permissions.canManageSettings}
                  >
                    <MenuItem value="thermal">Reçu thermique (80mm)</MenuItem>
                    <MenuItem value="a4">A4</MenuItem>
                  </Select>
                </FormControl>
                <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                  Choisissez le format de papier pour l'impression des reçus
                </Typography>
              </Grid>
              {permissions.canManageSettings && (
                <Grid item xs={12}>
                  <Button
                    variant="contained"
                    startIcon={<Save />}
                    onClick={handleSavePrintingSettings}
                  >
                    Sauvegarder
                  </Button>
                </Grid>
              )}
            </Grid>
          </CardContent>
        </Card>
      </CustomTabPanel>

      {/* Categories Tab */}
      <CustomTabPanel value={tabValue} index={3}>
        <Card>
          <CardHeader
            title="Gestion des Catégories"
            avatar={<Category />}
            action={
              permissions.canManageSettings && (
                <Button
                  variant="contained"
                  startIcon={<Add />}
                  onClick={() => handleOpenCategoryDialog()}
                >
                  Nouvelle Catégorie
                </Button>
              )
            }
          />
          <CardContent>
            <List>
              {categories.map((category, index) => (
                <React.Fragment key={category.id}>
                  <ListItem>
                    <Box
                      sx={{
                        width: 20,
                        height: 20,
                        backgroundColor: category.couleur,
                        borderRadius: 1,
                        mr: 2,
                      }}
                    />
                    <ListItemText
                      primary={category.nom}
                      secondary={category.description}
                    />
                    {permissions.canManageSettings && (
                      <ListItemSecondaryAction>
                        <IconButton
                          edge="end"
                          onClick={() => handleOpenCategoryDialog(category)}
                          sx={{ mr: 1 }}
                        >
                          <Edit />
                        </IconButton>
                        <IconButton
                          edge="end"
                          color="error"
                          onClick={() => handleDeleteCategory(category)}
                        >
                          <Delete />
                        </IconButton>
                      </ListItemSecondaryAction>
                    )}
                  </ListItem>
                  {index < categories.length - 1 && <Divider />}
                </React.Fragment>
              ))}
            </List>
          </CardContent>
        </Card>
      </CustomTabPanel>

      {/* Backup Tab */}
      <CustomTabPanel value={tabValue} index={4}>
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader
                title="Sauvegarde des Données"
                avatar={<Download />}
              />
              <CardContent>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Exportez toutes vos données dans un fichier JSON pour créer une sauvegarde.
                </Typography>
                <Button
                  variant="contained"
                  startIcon={<Download />}
                  onClick={handleExportData}
                  fullWidth
                  sx={{ mt: 2 }}
                >
                  Exporter les Données
                </Button>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardHeader
                title="Restauration des Données"
                avatar={<Upload />}
              />
              <CardContent>
                <Typography variant="body2" color="text.secondary" gutterBottom>
                  Importez un fichier de sauvegarde pour restaurer vos données.
                </Typography>
                <input
                  accept=".json"
                  style={{ display: 'none' }}
                  id="import-file"
                  type="file"
                  onChange={handleImportData}
                />
                <label htmlFor="import-file">
                  <Button
                    variant="outlined"
                    component="span"
                    startIcon={<Upload />}
                    fullWidth
                    sx={{ mt: 2 }}
                  >
                    Importer les Données
                  </Button>
                </label>
              </CardContent>
            </Card>
          </Grid>

          {adaptiveAuthService.hasRole(['super_admin']) && (
            <Grid item xs={12}>
              <Card>
                <CardHeader
                  title="Réinitialisation"
                  avatar={<RestoreFromTrash />}
                />
                <CardContent>
                  <Alert severity="warning" sx={{ mb: 2 }}>
                    <Typography variant="body2">
                      <strong>Attention:</strong> Cette action supprimera toutes les données
                      et restaurera les paramètres par défaut. Cette action est irréversible.
                    </Typography>
                  </Alert>
                  <Button
                    variant="outlined"
                    color="error"
                    startIcon={<RestoreFromTrash />}
                    onClick={handleResetData}
                  >
                    Réinitialiser toutes les Données
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          )}
        </Grid>
      </CustomTabPanel>

      {/* CSV Data Management Tab */}
      <CustomTabPanel value={tabValue} index={5}>
        <CSVManager
          onSuccess={(message) => {
            setSuccess(message);
            setTimeout(() => setSuccess(''), 3000);
          }}
          onError={(message) => {
            setError(message);
            setTimeout(() => setError(''), 5000);
          }}
        />
      </CustomTabPanel>



      {/* Category Dialog */}
      <Dialog open={openCategoryDialog} onClose={handleCloseCategoryDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingCategory ? 'Modifier la Catégorie' : 'Nouvelle Catégorie'}
        </DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}

          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Nom de la catégorie *"
                value={categoryForm.nom}
                onChange={(e) => setCategoryForm({ ...categoryForm, nom: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={2}
                value={categoryForm.description}
                onChange={(e) => setCategoryForm({ ...categoryForm, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Couleur"
                type="color"
                value={categoryForm.couleur}
                onChange={(e) => setCategoryForm({ ...categoryForm, couleur: e.target.value })}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <ColorLens />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12}>
              <Box display="flex" alignItems="center" gap={1}>
                <Typography variant="body2">Aperçu:</Typography>
                <Chip
                  label={categoryForm.nom || 'Nom de la catégorie'}
                  sx={{ backgroundColor: categoryForm.couleur, color: 'white' }}
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseCategoryDialog}>Annuler</Button>
          <Button onClick={handleSaveCategory} variant="contained">
            {editingCategory ? 'Mettre à jour' : 'Créer'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SettingsPage;
