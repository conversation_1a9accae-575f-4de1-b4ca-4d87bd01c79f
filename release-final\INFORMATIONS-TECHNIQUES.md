# SmartBoutique - Informations Techniques

## 📋 Spécifications Techniques

### Architecture de l'Application
- **Framework** : Electron 28.3.3 + React 18.3.1
- **Interface** : Material-UI 5.16.10
- **Stockage** : CSV local (migration SQLite disponible)
- **Plateforme** : Windows 64-bit (x64)
- **Taille** : ~150 MB installé

### Technologies Utilisées
- **Frontend** : React, TypeScript, Material-UI
- **Backend** : Node.js intégré (Electron)
- **Base de données** : Fichiers CSV locaux
- **Graphiques** : Chart.js + React-ChartJS-2
- **Impression** : API d'impression Electron native

## 🔧 Configuration Système

### Prérequis Windows
- **OS** : Windows 10 version 1903 ou plus récent
- **Architecture** : x64 (64-bit) uniquement
- **Runtime** : Aucun runtime externe requis (tout inclus)
- **Permissions** : Utilisateur standard (pas d'admin requis pour l'exécution)

### Ressources Système
- **RAM** : 4 GB minimum, 8 GB recommandé
- **CPU** : Compatible x64, 2 GHz recommandé
- **Disque** : 500 MB libre minimum
- **Réseau** : Aucune connexion requise (fonctionnement offline)

## 📁 Structure des Fichiers

### Version Portable
```
SmartBoutique-Portable-1.1.0.exe
├── Application complète auto-extractible
├── Données stockées dans %APPDATA%\SmartBoutique
└── Aucune modification du registre
```

### Version Installée
```
C:\Program Files\SmartBoutique\
├── SmartBoutique.exe (application principale)
├── resources\ (ressources de l'application)
├── locales\ (fichiers de localisation)
└── *.dll (bibliothèques système)

%APPDATA%\SmartBoutique\
├── data\ (données utilisateur)
├── config\ (configuration)
└── logs\ (fichiers de log)
```

## 💾 Stockage des Données

### Emplacement des Données
- **Windows** : `%APPDATA%\SmartBoutique\data\`
- **Format** : Fichiers CSV avec encodage UTF-8
- **Sauvegarde** : Copie manuelle du dossier data

### Fichiers de Données
```
data/
├── products.csv (produits)
├── sales.csv (ventes)
├── users.csv (utilisateurs)
├── categories.csv (catégories)
├── expenses.csv (dépenses)
├── debts.csv (dettes)
├── employee-payments.csv (paiements employés)
└── settings.json (paramètres)
```

### Migration SQLite (Optionnelle)
- Support SQLite avec better-sqlite3
- Migration automatique depuis CSV
- Performance améliorée pour gros volumes
- Activation via paramètres avancés

## 🔒 Sécurité

### Modèle de Sécurité
- **Données locales** : Aucune transmission réseau
- **Chiffrement** : Données en clair (CSV)
- **Authentification** : Basique (configurable)
- **Permissions** : Basées sur les rôles utilisateur

### Considérations de Déploiement
- Aucun port réseau ouvert
- Pas de service Windows installé
- Exécution en mode utilisateur standard
- Compatible avec les politiques de sécurité d'entreprise

## 🖨️ Impression

### Système d'Impression
- **API** : Electron native printing
- **Formats** : Reçus thermiques (80mm) et A4
- **Pilotes** : Utilise les pilotes Windows installés
- **Configuration** : Automatique via dialogue Windows

### Imprimantes Supportées
- Toutes imprimantes avec pilotes Windows
- Imprimantes thermiques POS (80mm)
- Imprimantes laser/jet d'encre standard
- Impression PDF intégrée

## 🌐 Localisation

### Support Linguistique
- **Interface** : Français complet
- **Formats** : Dates, nombres, devises localisés
- **Devises** : CDF (Franc Congolais) et USD
- **Taux de change** : Configuration manuelle

## 🔧 Déploiement d'Entreprise

### Installation Silencieuse
```bash
# Installation silencieuse (version installateur)
SmartBoutique-Installer-1.1.0.exe /S

# Désinstallation silencieuse
"C:\Program Files\SmartBoutique\Uninstall SmartBoutique.exe" /S
```

### Déploiement de Masse
- **GPO** : Compatible avec les stratégies de groupe
- **SCCM** : Déploiement via System Center
- **Portable** : Copie simple sur partage réseau
- **Configuration** : Fichiers de config centralisés possibles

### Paramètres Centralisés
```json
// settings.json (exemple)
{
  "boutiqueName": "Ma Boutique",
  "currency": "CDF",
  "exchangeRate": 2800,
  "language": "fr",
  "theme": "light"
}
```

## 📊 Monitoring et Logs

### Fichiers de Log
- **Emplacement** : `%APPDATA%\SmartBoutique\logs\`
- **Format** : Texte simple avec timestamps
- **Rotation** : Automatique (taille max 10MB)
- **Niveaux** : Error, Warning, Info, Debug

### Monitoring de Performance
- Utilisation mémoire : ~100-200 MB
- CPU : Minimal en arrière-plan
- Disque : Croissance selon données
- Réseau : Aucun trafic

## 🔄 Mise à Jour

### Processus de Mise à Jour
- **Méthode** : Installation manuelle
- **Données** : Préservées automatiquement
- **Configuration** : Migrée vers nouvelle version
- **Rollback** : Réinstallation version précédente

### Compatibilité des Versions
- Migration automatique des données
- Rétrocompatibilité assurée
- Tests recommandés avant déploiement

## 🛠️ Dépannage Technique

### Problèmes Courants
1. **Erreur de démarrage**
   - Vérifier permissions dossier %APPDATA%
   - Exécuter en tant qu'administrateur
   - Vérifier intégrité des fichiers

2. **Performance dégradée**
   - Vérifier taille des fichiers CSV
   - Considérer migration SQLite
   - Augmenter RAM disponible

3. **Problèmes d'impression**
   - Vérifier pilotes imprimante
   - Tester impression Windows standard
   - Vérifier paramètres de format

### Outils de Diagnostic
- Logs détaillés dans %APPDATA%\SmartBoutique\logs\
- Mode debug activable via paramètres
- Outils Windows standard (Event Viewer, Task Manager)

## 📞 Support Technique

### Informations à Collecter
- Version de Windows (winver)
- Version SmartBoutique (dans À propos)
- Fichiers de log récents
- Description détaillée du problème
- Étapes de reproduction

### Contact Support
- **Email** : <EMAIL>
- **Objet** : [TECH] Description du problème
- **Joindre** : Logs et captures d'écran

---

**SmartBoutique v1.1.0 - Informations Techniques**  
*Document destiné aux administrateurs IT et support technique*
