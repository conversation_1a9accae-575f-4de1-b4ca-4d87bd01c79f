import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  Grid,
  Card,
  CardContent,
  InputAdornment,
  Autocomplete,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Divider,
  RadioGroup,
  FormControlLabel,
  Radio,
  FormLabel,
  Checkbox,
  CircularProgress,
} from '@mui/material';
import {
  Add,
  Search,
  PointOfSale,
  Receipt,
  CreditCard,
  AccountBalance,
  Phone,
  Remove,
  ShoppingCart,
} from '@mui/icons-material';

// Services
import { adaptiveStorageService } from '@/services/adaptive-storage';
import { adaptiveAuthService } from '@/services/adaptive-auth';
import { ReceiptService } from '@/services/receipt';

// Types
import { Product, Sale, SaleItem, Debt } from '@/types';

// Components
import { ReceiptPreviewModal } from '@/components/Receipt/ReceiptPreviewModal';
import { QuantityInput } from '@/components/QuantityInput';

// Utils
import { format } from 'date-fns';
import { fr } from 'date-fns/locale';
import { formatSingleCurrency, formatDualCurrencyForCard } from '@/utils';

const SalesPage: React.FC = () => {
  const [sales, setSales] = useState<Sale[]>([]);
  const [products, setProducts] = useState<Product[]>([]);
  const [filteredSales, setFilteredSales] = useState<Sale[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [openDialog, setOpenDialog] = useState(false);
  const [openViewDialog, setOpenViewDialog] = useState(false);
  const [viewingSale, setViewingSale] = useState<Sale | null>(null);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  // Sale form state
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);
  const [customerName, setCustomerName] = useState('');
  const [customerPhone, setCustomerPhone] = useState('');
  const [customerAddress, setCustomerAddress] = useState('');
  const [paymentMethod, setPaymentMethod] = useState<'cash' | 'card' | 'mobile_money'>('cash');
  const [saleType, setSaleType] = useState<'cash' | 'credit'>('cash');
  const [saleCurrency, setSaleCurrency] = useState<'CDF' | 'USD'>('CDF'); // Currency selection for the sale
  const [dueDate, setDueDate] = useState('');
  const [notes, setNotes] = useState('');
  const [printReceipt, setPrintReceipt] = useState(false);
  const [settings, setSettings] = useState<any>({ tauxChangeUSDCDF: 2800 }); // Add settings state

  // Receipt printing state
  const [receiptPreviewOpen, setReceiptPreviewOpen] = useState(false);
  const [receiptData, setReceiptData] = useState<any>(null);
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);

  const permissions = adaptiveAuthService.getUserPermissions();
  const currentUser = adaptiveAuthService.getCurrentUser();

  // Helper function to safely parse dates
  const parseDate = (dateString: string): Date | null => {
    try {
      const date = new Date(dateString);
      // Check if the date is valid
      if (isNaN(date.getTime())) {
        console.warn('Invalid date found:', dateString);
        return null;
      }
      return date;
    } catch (error) {
      console.warn('Error parsing date:', dateString, error);
      return null;
    }
  };

  useEffect(() => {
    loadData();
  }, []);

  useEffect(() => {
    filterSales();
  }, [sales, searchTerm]);

  // Update rowsPerPage when filteredSales changes and "Voir tout" is selected
  useEffect(() => {
    if (rowsPerPage === -1) {
      // Force re-render when showing all items and data changes
      setRowsPerPage(filteredSales.length || 1);
    }
  }, [filteredSales.length, rowsPerPage]);

  const loadData = async () => {
    const salesData = await adaptiveStorageService.getSales();
    const productsData = await adaptiveStorageService.getProducts();
    const settingsData = await adaptiveStorageService.getSettings();
    setSales(salesData);
    setProducts(productsData);
    setSettings(settingsData);
  };

  const filterSales = () => {
    let filtered = sales;

    if (searchTerm) {
      filtered = filtered.filter(sale =>
        sale.nomClient.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sale.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
        sale.vendeur.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    setFilteredSales(filtered);
  };

  const handleOpenDialog = () => {
    resetForm();
    setOpenDialog(true);
    setError('');
    setSuccess('');
  };

  const handleCloseDialog = () => {
    setOpenDialog(false);
    resetForm();
    setError('');
    setSuccess('');
  };

  const resetForm = () => {
    setSaleItems([]);
    setSelectedProduct(null);
    setQuantity(1);
    setCustomerName('');
    setCustomerPhone('');
    setCustomerAddress('');
    setPaymentMethod('cash');
    setSaleType('cash');
    setSaleCurrency('CDF'); // Reset to default currency
    setDueDate('');
    setNotes('');
    setPrintReceipt(false);
  };

  const handleAddProduct = () => {
    if (!selectedProduct) {
      setError('Veuillez sélectionner un produit');
      return;
    }

    if (quantity <= 0) {
      setError('La quantité doit être supérieure à 0');
      return;
    }

    if (quantity > selectedProduct.stock) {
      setError('Quantité insuffisante en stock');
      return;
    }

    // Check if product already in cart
    const existingItemIndex = saleItems.findIndex(item => item.produitId === selectedProduct.id);

    if (existingItemIndex >= 0) {
      const updatedItems = [...saleItems];
      const newQuantity = updatedItems[existingItemIndex].quantite + quantity;

      if (newQuantity > selectedProduct.stock) {
        setError('Quantité totale dépasse le stock disponible');
        return;
      }

      updatedItems[existingItemIndex] = {
        ...updatedItems[existingItemIndex],
        quantite: newQuantity,
        totalCDF: newQuantity * selectedProduct.prixCDF,
        totalUSD: selectedProduct.prixUSD ? newQuantity * selectedProduct.prixUSD : undefined,
      };
      setSaleItems(updatedItems);
    } else {
      const newItem: SaleItem = {
        produitId: selectedProduct.id,
        nomProduit: selectedProduct.nom,
        quantite: quantity,
        prixUnitaireCDF: selectedProduct.prixCDF,
        prixUnitaireUSD: selectedProduct.prixUSD,
        totalCDF: quantity * selectedProduct.prixCDF,
        totalUSD: selectedProduct.prixUSD ? quantity * selectedProduct.prixUSD : undefined,
      };
      setSaleItems([...saleItems, newItem]);
    }

    setSelectedProduct(null);
    setQuantity(1);
    setError('');
  };

  const handleRemoveProduct = (index: number) => {
    const updatedItems = saleItems.filter((_, i) => i !== index);
    setSaleItems(updatedItems);
  };

  const handleUpdateQuantity = (index: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveProduct(index);
      return;
    }

    const item = saleItems[index];
    const product = products.find(p => p.id === item.produitId);

    if (product && newQuantity > product.stock) {
      setError(`Quantité maximale disponible: ${product.stock}`);
      return;
    }

    const updatedItems = [...saleItems];
    updatedItems[index] = {
      ...item,
      quantite: newQuantity,
      totalCDF: newQuantity * item.prixUnitaireCDF,
      totalUSD: item.prixUnitaireUSD ? newQuantity * item.prixUnitaireUSD : undefined,
    };
    setSaleItems(updatedItems);
    setError('');
  };

  const calculateTotal = () => {
    return saleItems.reduce((sum, item) => ({
      CDF: sum.CDF + item.totalCDF,
      USD: sum.USD + (item.totalUSD || 0),
    }), { CDF: 0, USD: 0 });
  };

  // Helper function to get price in selected currency
  const getPriceInSelectedCurrency = (product: Product) => {
    if (saleCurrency === 'USD') {
      return {
        price: product.prixUSD || (product.prixCDF / (settings?.tauxChangeUSDCDF || 2800)),
        currency: 'USD',
        symbol: '$'
      };
    }
    return {
      price: product.prixCDF,
      currency: 'CDF',
      symbol: ''
    };
  };

  // Helper function to format price in selected currency
  const formatPriceInSelectedCurrency = (product: Product) => {
    const priceInfo = getPriceInSelectedCurrency(product);
    return `${priceInfo.symbol}${priceInfo.price.toLocaleString('fr-FR', {
      minimumFractionDigits: priceInfo.currency === 'USD' ? 2 : 0,
      maximumFractionDigits: priceInfo.currency === 'USD' ? 2 : 0
    })} ${priceInfo.currency}`;
  };

  // Helper function to format cart item price in selected currency
  const formatCartItemPrice = (item: SaleItem) => {
    if (saleCurrency === 'USD') {
      const unitPrice = item.prixUnitaireUSD || (item.prixUnitaireCDF / (settings?.tauxChangeUSDCDF || 2800));
      const total = item.totalUSD || (item.totalCDF / (settings?.tauxChangeUSDCDF || 2800));
      return {
        unitPrice: `$${unitPrice.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} USD`,
        total: `$${total.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} USD`
      };
    }
    return {
      unitPrice: `${item.prixUnitaireCDF.toLocaleString('fr-FR')} CDF`,
      total: `${item.totalCDF.toLocaleString('fr-FR')} CDF`
    };
  };

  // Helper function to format total in selected currency
  const formatTotalInSelectedCurrency = () => {
    const total = calculateTotal();
    if (saleCurrency === 'USD') {
      return `$${total.USD.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })} USD`;
    }
    return `${total.CDF.toLocaleString('fr-FR')} CDF`;
  };

  const handleSaveSale = async () => {
    // Validation
    if (saleItems.length === 0) {
      setError('Veuillez ajouter au moins un produit');
      return;
    }

    // Customer name is now optional - will default to "Client" if empty

    if (saleType === 'credit' && !dueDate) {
      setError('La date d\'échéance est requise pour les ventes à crédit');
      return;
    }

    const total = calculateTotal();
    const now = new Date().toISOString();

    // Generate receipt number if printing is requested or auto-print is enabled
    const shouldPrint = printReceipt || settings?.impression?.impressionAutomatique || false;
    const receiptNumber = shouldPrint ? await ReceiptService.generateSalesReceiptNumber() : undefined;

    // Create sale
    const newSale: Sale = {
      id: `VTE-${Date.now()}`,
      produits: saleItems,
      nomClient: customerName.trim() || 'Client',
      telephoneClient: customerPhone.trim() || undefined,
      adresseClient: customerAddress.trim() || undefined,
      totalCDF: total.CDF,
      totalUSD: total.USD,
      methodePaiement: paymentMethod,
      typeVente: saleType,
      datevente: now,
      vendeur: currentUser?.nom || 'Inconnu',
      notes: notes.trim() || undefined,
      numeroRecu: receiptNumber,
    };

    // Update product stocks
    const updatedProducts = products.map(product => {
      const saleItem = saleItems.find(item => item.produitId === product.id);
      if (saleItem) {
        return {
          ...product,
          stock: product.stock - saleItem.quantite,
          dateModification: now,
        };
      }
      return product;
    });

    // Save sale and update products
    const updatedSales = [...sales, newSale];
    setSales(updatedSales);
    setProducts(updatedProducts);
    await adaptiveStorageService.setSales(updatedSales);
    await adaptiveStorageService.setProducts(updatedProducts);

    // Create debt if credit sale
    if (saleType === 'credit') {
      const newDebt: Debt = {
        id: `DET-${Date.now()}`,
        venteId: newSale.id,
        nomClient: customerName.trim() || 'Client',
        telephoneClient: customerPhone.trim() || undefined,
        adresseClient: customerAddress.trim() || undefined,
        montantTotalCDF: total.CDF,
        montantTotalUSD: total.USD,
        montantPayeCDF: 0,
        montantPayeUSD: 0,
        montantRestantCDF: total.CDF,
        montantRestantUSD: total.USD,
        dateCreation: now,
        dateEcheance: dueDate,
        statut: 'active',
        statutPaiement: 'impaye', // New debts start as unpaid
        paiements: [],
        notes: notes.trim() || undefined,
        deviseVente: saleCurrency, // Store the selected currency for the sale
      };

      const debts = await adaptiveStorageService.getDebts();
      await adaptiveStorageService.setDebts([...debts, newDebt]);
    }

    setSuccess('Vente enregistrée avec succès');

    // Handle receipt printing if requested
    if (shouldPrint) {
      try {
        setIsGeneratingReceipt(true);
        const receiptData = await ReceiptService.createSalesReceiptData(newSale);
        setReceiptData(receiptData);
        setReceiptPreviewOpen(true);
      } catch (error) {
        console.error('Erreur lors de la génération du reçu:', error);
        setError('Erreur lors de la génération du reçu');
      } finally {
        setIsGeneratingReceipt(false);
      }
    }

    setTimeout(() => {
      handleCloseDialog();
    }, 1500);
  };

  const handleViewSale = (sale: Sale) => {
    setViewingSale(sale);
    setOpenViewDialog(true);
  };



  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case 'cash': return <Receipt />;
      case 'card': return <CreditCard />;
      case 'mobile_money': return <Phone />;
      default: return <Receipt />;
    }
  };

  const getPaymentMethodLabel = (method: string) => {
    switch (method) {
      case 'cash': return 'Cash';
      case 'card': return 'Carte';
      case 'mobile_money': return 'Mobile Money';
      default: return method;
    }
  };

  const handleChangePage = (_: unknown, newPage: number) => {
    // Don't change page if showing all items
    if (rowsPerPage !== -1) {
      setPage(newPage);
    }
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(event.target.value, 10);
    setRowsPerPage(value);
    setPage(0); // Reset to first page when changing rows per page
  };

  // Stats with safe date parsing
  const todaySales = sales.filter(sale => {
    const saleDate = parseDate(sale.datevente);
    if (!saleDate) return false;
    const today = new Date();
    return saleDate.toDateString() === today.toDateString();
  });

  const todayRevenue = todaySales.reduce((sum, sale) => sum + sale.totalCDF, 0);
  const totalSales = sales.length;
  const totalRevenue = sales.reduce((sum, sale) => sum + sale.totalCDF, 0);
  const averageSale = totalSales > 0 ? totalRevenue / totalSales : 0;

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">
          Gestion des Ventes
        </Typography>
        {permissions.canManageSales && (
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={handleOpenDialog}
          >
            Nouvelle Vente
          </Button>
        )}
      </Box>

      {/* Success/Error Messages */}
      {success && (
        <Alert severity="success" sx={{ mb: 2 }} onClose={() => setSuccess('')}>
          {success}
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Ventes du jour
                  </Typography>
                  <Typography variant="h6">{todaySales.length}</Typography>
                  <Typography variant="body2" color="primary" fontWeight="medium">
                    {formatDualCurrencyForCard(todayRevenue, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(todayRevenue, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(todayRevenue, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <PointOfSale color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Total Ventes
                  </Typography>
                  <Typography variant="h6">{totalSales}</Typography>
                  <Typography variant="body2" color="success.main" fontWeight="medium">
                    {formatDualCurrencyForCard(totalRevenue, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(totalRevenue, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(totalRevenue, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <ShoppingCart color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Vente Moyenne
                  </Typography>
                  <Typography variant="h6" fontWeight="medium">
                    {formatDualCurrencyForCard(averageSale, settings.tauxChangeUSDCDF).primaryAmount} {formatDualCurrencyForCard(averageSale, settings.tauxChangeUSDCDF).primaryCurrency}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    ≈ ${formatDualCurrencyForCard(averageSale, settings.tauxChangeUSDCDF).secondaryAmount}
                  </Typography>
                </Box>
                <AccountBalance color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Ventes à Crédit
                  </Typography>
                  <Typography variant="h6">
                    {sales.filter(s => s.typeVente === 'credit').length}
                  </Typography>
                </Box>
                <CreditCard color="warning" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Search */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <TextField
          fullWidth
          placeholder="Rechercher par client, ID vente ou vendeur..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          InputProps={{
            startAdornment: (
              <InputAdornment position="start">
                <Search />
              </InputAdornment>
            ),
          }}
        />
      </Paper>

      {/* Sales Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Produits Vendus</TableCell>
              <TableCell>Client</TableCell>
              <TableCell>Vendeur</TableCell>
              <TableCell align="right">Total CDF</TableCell>
              <TableCell align="right">Total USD</TableCell>
              <TableCell align="center">Paiement</TableCell>
              <TableCell align="center">Type</TableCell>
              <TableCell>Date</TableCell>
              <TableCell align="center">Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {(rowsPerPage === -1 ? filteredSales : filteredSales.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage))
              .map((sale) => (
                <TableRow
                  key={sale.id}
                  hover
                  onClick={() => handleViewSale(sale)}
                  sx={{ cursor: 'pointer' }}
                >
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2" color="primary" gutterBottom>
                        {sale.produits.map(p => p.nomProduit).join(', ')}
                      </Typography>
                      <Typography variant="caption" color="text.secondary" display="block">
                        ID: {sale.id} • {sale.produits.length} article{sale.produits.length > 1 ? 's' : ''}
                      </Typography>
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Box>
                      <Typography variant="subtitle2">{sale.nomClient}</Typography>
                      {sale.telephoneClient && (
                        <Typography variant="caption" color="text.secondary">
                          {sale.telephoneClient}
                        </Typography>
                      )}
                    </Box>
                  </TableCell>
                  <TableCell>{sale.vendeur}</TableCell>
                  <TableCell align="right">
                    {formatSingleCurrency(sale.totalCDF, 'CDF')}
                  </TableCell>
                  <TableCell align="right">
                    {sale.totalUSD ? formatSingleCurrency(sale.totalUSD, 'USD') : '-'}
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      icon={getPaymentMethodIcon(sale.methodePaiement)}
                      label={getPaymentMethodLabel(sale.methodePaiement)}
                      size="small"
                    />
                  </TableCell>
                  <TableCell align="center">
                    <Chip
                      label={sale.typeVente === 'cash' ? 'Cash' : 'Crédit'}
                      color={sale.typeVente === 'cash' ? 'success' : 'warning'}
                      size="small"
                    />
                  </TableCell>
                  <TableCell>
                    {(() => {
                      const saleDate = parseDate(sale.datevente);
                      return saleDate
                        ? format(saleDate, 'dd/MM/yyyy HH:mm', { locale: fr })
                        : 'Date invalide';
                    })()}
                  </TableCell>
                  <TableCell align="center">
                    <Tooltip title="Voir détails">
                      <IconButton
                        size="small"
                        onClick={() => handleViewSale(sale)}
                      >
                        <Receipt fontSize="small" />
                      </IconButton>
                    </Tooltip>
                  </TableCell>
                </TableRow>
              ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[
            5,
            10,
            25,
            50,
            100,
            { label: 'Voir tout', value: -1 }
          ]}
          component="div"
          count={filteredSales.length}
          rowsPerPage={rowsPerPage === -1 ? filteredSales.length : rowsPerPage}
          page={rowsPerPage === -1 ? 0 : page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
          labelRowsPerPage="Lignes par page:"
          labelDisplayedRows={({ from, to, count }) => {
            if (rowsPerPage === -1) {
              return `Affichage de tous les ${count} éléments`;
            }
            return `${from}-${to} sur ${count !== -1 ? count : `plus de ${to}`}`;
          }}
        />
      </TableContainer>

      {/* New Sale Dialog */}
      <Dialog open={openDialog} onClose={handleCloseDialog} maxWidth="lg" fullWidth>
        <DialogTitle>Nouvelle Vente</DialogTitle>
        <DialogContent>
          {error && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {error}
            </Alert>
          )}
          {success && (
            <Alert severity="success" sx={{ mb: 2 }}>
              {success}
            </Alert>
          )}

          {/* Currency Selection - Moved to top for better UX */}
          <Box sx={{ mb: 3, p: 2, bgcolor: 'primary.50', borderRadius: 1, border: '1px solid', borderColor: 'primary.200' }}>
            <Typography variant="subtitle2" gutterBottom sx={{ fontWeight: 'bold', color: 'primary.main' }}>
              💱 Devise de la Vente
            </Typography>
            <FormControl fullWidth size="small">
              <InputLabel>Sélectionnez la devise pour cette vente</InputLabel>
              <Select
                value={saleCurrency}
                label="Sélectionnez la devise pour cette vente"
                onChange={(e) => setSaleCurrency(e.target.value as 'CDF' | 'USD')}
              >
                <MenuItem value="CDF">
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography>🇨🇩</Typography>
                    <Typography>CDF (Franc Congolais) - Devise principale</Typography>
                  </Box>
                </MenuItem>
                <MenuItem value="USD">
                  <Box display="flex" alignItems="center" gap={1}>
                    <Typography>🇺🇸</Typography>
                    <Typography>USD (Dollar Américain)</Typography>
                  </Box>
                </MenuItem>
              </Select>
            </FormControl>
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              Les prix des produits seront affichés dans la devise sélectionnée
            </Typography>
          </Box>

          <Grid container spacing={3}>
            {/* Product Selection */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Sélection des Produits
              </Typography>
              <Grid container spacing={2} alignItems="center">
                <Grid item xs={12} md={6}>
                  <Autocomplete
                    options={products.filter(p => p.stock > 0)}
                    getOptionLabel={(option) => `${option.nom} - ${formatPriceInSelectedCurrency(option)} - Stock: ${option.stock}`}
                    value={selectedProduct}
                    onChange={(_, newValue) => setSelectedProduct(newValue)}
                    renderInput={(params) => (
                      <TextField {...params} label="Produit" fullWidth />
                    )}
                    renderOption={(props, option) => (
                      <Box component="li" {...props}>
                        <Box sx={{ display: 'flex', flexDirection: 'column', width: '100%' }}>
                          <Typography variant="body1" sx={{ fontWeight: 500 }}>
                            {option.nom}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Prix: {formatPriceInSelectedCurrency(option)} • Stock: {option.stock} • Code: {option.codeQR}
                          </Typography>
                        </Box>
                      </Box>
                    )}
                  />
                </Grid>
                <Grid item xs={12} md={3}>
                  <Box>
                    <QuantityInput
                      value={quantity}
                      onChange={setQuantity}
                      min={1}
                      max={selectedProduct?.stock || 999}
                      size="small"
                      showButtons={true}
                      allowDirectInput={true}
                      label="Quantité"
                    />
                    {selectedProduct && (
                      <Typography variant="caption" color="text.secondary" display="block" sx={{ mt: 1 }}>
                        Stock disponible: {selectedProduct.stock}
                      </Typography>
                    )}
                  </Box>
                </Grid>
                <Grid item xs={12} md={3}>
                  <Button
                    fullWidth
                    variant="contained"
                    onClick={handleAddProduct}
                    disabled={!selectedProduct}
                  >
                    Ajouter
                  </Button>
                </Grid>
              </Grid>

              {/* Selected Product Price Display */}
              {selectedProduct && (
                <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.300' }}>
                  <Grid container spacing={2} alignItems="center">
                    <Grid item xs={12} md={6}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                        Produit sélectionné: {selectedProduct.nom}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Code: {selectedProduct.codeQR} • Catégorie: {selectedProduct.categorie}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" color="primary" sx={{ fontWeight: 'bold' }}>
                        Prix unitaire: {formatPriceInSelectedCurrency(selectedProduct)}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        Devise: {saleCurrency}
                      </Typography>
                    </Grid>
                    <Grid item xs={12} md={3}>
                      <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                        Total: {(() => {
                          const priceInfo = getPriceInSelectedCurrency(selectedProduct);
                          const total = priceInfo.price * quantity;
                          return `${priceInfo.symbol}${total.toLocaleString('fr-FR', {
                            minimumFractionDigits: priceInfo.currency === 'USD' ? 2 : 0,
                            maximumFractionDigits: priceInfo.currency === 'USD' ? 2 : 0
                          })} ${priceInfo.currency}`;
                        })()}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {quantity} × {formatPriceInSelectedCurrency(selectedProduct)}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>
              )}
            </Grid>

            {/* Cart */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Panier ({saleItems.length} articles) - Devise: {saleCurrency}
              </Typography>
              {saleItems.length === 0 ? (
                <Alert severity="info">Aucun produit ajouté</Alert>
              ) : (
                <List>
                  {saleItems.map((item, index) => {
                    const priceInfo = formatCartItemPrice(item);
                    return (
                      <React.Fragment key={index}>
                        <ListItem>
                          <ListItemText
                            primary={
                              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                                {item.nomProduit}
                              </Typography>
                            }
                            secondary={`Prix unitaire: ${priceInfo.unitPrice}`}
                          />
                          <ListItemSecondaryAction>
                            <Box display="flex" alignItems="center" gap={1}>
                              <QuantityInput
                                value={item.quantite}
                                onChange={(newQuantity) => handleUpdateQuantity(index, newQuantity)}
                                min={1}
                                max={products.find(p => p.id === item.produitId)?.stock || 999}
                                size="small"
                                showButtons={true}
                                allowDirectInput={true}
                              />
                              <Typography variant="subtitle1" sx={{ minWidth: '120px', textAlign: 'right', fontWeight: 'bold', color: 'primary.main' }}>
                                {priceInfo.total}
                              </Typography>
                              <IconButton
                                size="small"
                                color="error"
                                onClick={() => handleRemoveProduct(index)}
                                title="Supprimer l'article"
                              >
                                <ShoppingCart fontSize="small" />
                              </IconButton>
                            </Box>
                          </ListItemSecondaryAction>
                        </ListItem>
                        {index < saleItems.length - 1 && <Divider />}
                      </React.Fragment>
                    );
                  })}
                  <Divider sx={{ my: 2 }} />
                  <ListItem sx={{ bgcolor: 'primary.50', borderRadius: 1 }}>
                    <ListItemText
                      primary={
                        <Typography variant="h6" sx={{ fontWeight: 'bold', color: 'primary.main' }}>
                          Total: {formatTotalInSelectedCurrency()}
                        </Typography>
                      }
                      secondary={
                        <Typography variant="body2" color="text.secondary">
                          {saleCurrency === 'USD'
                            ? `≈ ${formatSingleCurrency(calculateTotal().CDF, 'CDF')} (taux: ${settings?.tauxChangeUSDCDF || 2800})`
                            : `≈ ${formatSingleCurrency(calculateTotal().USD, 'USD')} (taux: ${settings?.tauxChangeUSDCDF || 2800})`
                          }
                        </Typography>
                      }
                    />
                  </ListItem>
                </List>
              )}
            </Grid>

            {/* Customer Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Informations Client
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Nom du client"
                    placeholder="Client (par défaut)"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Téléphone"
                    value={customerPhone}
                    onChange={(e) => setCustomerPhone(e.target.value)}
                  />
                </Grid>
                <Grid item xs={12} md={4}>
                  <TextField
                    fullWidth
                    label="Adresse"
                    value={customerAddress}
                    onChange={(e) => setCustomerAddress(e.target.value)}
                  />
                </Grid>
              </Grid>
            </Grid>

            {/* Payment Information */}
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Informations de Paiement
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} md={6}>
                  <FormControl component="fieldset">
                    <FormLabel component="legend">Type de vente</FormLabel>
                    <RadioGroup
                      row
                      value={saleType}
                      onChange={(e) => setSaleType(e.target.value as 'cash' | 'credit')}
                    >
                      <FormControlLabel value="cash" control={<Radio />} label="Cash" />
                      <FormControlLabel value="credit" control={<Radio />} label="Crédit" />
                    </RadioGroup>
                  </FormControl>
                </Grid>
                <Grid item xs={12} md={6}>
                  <FormControl fullWidth>
                    <InputLabel>Méthode de paiement</InputLabel>
                    <Select
                      value={paymentMethod}
                      label="Méthode de paiement"
                      onChange={(e) => setPaymentMethod(e.target.value as any)}
                    >
                      <MenuItem value="cash">Cash</MenuItem>
                      <MenuItem value="card">Carte</MenuItem>
                      <MenuItem value="mobile_money">Mobile Money</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {saleType === 'credit' && (
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Date d'échéance"
                      type="date"
                      value={dueDate}
                      onChange={(e) => setDueDate(e.target.value)}
                      InputLabelProps={{ shrink: true }}
                    />
                  </Grid>
                )}
                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Notes"
                    multiline
                    rows={2}
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                  />
                </Grid>
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <FormControlLabel
            control={
              <Checkbox
                checked={printReceipt}
                onChange={(e) => setPrintReceipt(e.target.checked)}
              />
            }
            label="Imprimer reçu"
            sx={{ mr: 'auto' }}
          />
          {isGeneratingReceipt && (
            <Box display="flex" alignItems="center" gap={1} mr={2}>
              <CircularProgress size={20} />
              <Typography variant="body2">Génération du reçu...</Typography>
            </Box>
          )}
          <Button onClick={handleCloseDialog}>Annuler</Button>
          <Button
            onClick={handleSaveSale}
            variant="contained"
            disabled={saleItems.length === 0 || isGeneratingReceipt}
          >
            Enregistrer la Vente
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Sale Dialog */}
      <Dialog open={openViewDialog} onClose={() => setOpenViewDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>Détails de la Vente</DialogTitle>
        <DialogContent>
          {viewingSale && (
            <Grid container spacing={2}>
              <Grid item xs={12}>
                <Typography variant="subtitle2" sx={{ fontSize: '1.1rem', fontWeight: 'bold' }}>
                  Produits Vendus:
                </Typography>
                <Typography variant="h6" color="primary" sx={{ mt: 0.5, mb: 1 }}>
                  {viewingSale.produits.map(p => p.nomProduit).join(', ')}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Référence Vente:</Typography>
                <Typography variant="body2" color="text.secondary">{viewingSale.id}</Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Date:</Typography>
                <Typography variant="body1">
                  {format(new Date(viewingSale.datevente), 'dd/MM/yyyy HH:mm', { locale: fr })}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Client:</Typography>
                <Typography variant="body1">{viewingSale.nomClient}</Typography>
                {viewingSale.telephoneClient && (
                  <Typography variant="body2" color="text.secondary">
                    {viewingSale.telephoneClient}
                  </Typography>
                )}
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Vendeur:</Typography>
                <Typography variant="body1">{viewingSale.vendeur}</Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="subtitle2" gutterBottom>
                  Produits:
                </Typography>
                <List dense>
                  {viewingSale.produits.map((item, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={item.nomProduit}
                        secondary={
                          <Box>
                            <Typography variant="body2" component="span">
                              {item.quantite} × {formatSingleCurrency(item.prixUnitaireCDF, 'CDF')}
                            </Typography>
                            <Typography variant="caption" display="block" color="text.secondary">
                              ≈ {item.quantite} × {formatSingleCurrency((item.prixUnitaireCDF / (settings?.tauxChangeUSDCDF || 2800)), 'USD')}
                            </Typography>
                          </Box>
                        }
                      />
                      <Box textAlign="right">
                        <Typography variant="body2" fontWeight="medium">
                          {formatSingleCurrency(item.totalCDF, 'CDF')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          ≈ {formatSingleCurrency((item.totalCDF / (settings?.tauxChangeUSDCDF || 2800)), 'USD')}
                        </Typography>
                      </Box>
                    </ListItem>
                  ))}
                  <Divider />
                  <ListItem>
                    <ListItemText
                      primary={
                        <Box>
                          <Typography variant="h6" color="primary" fontWeight="medium">
                            Total: {formatDualCurrencyForCard(viewingSale.totalCDF, settings?.tauxChangeUSDCDF || 2800).primaryAmount} {formatDualCurrencyForCard(viewingSale.totalCDF, settings?.tauxChangeUSDCDF || 2800).primaryCurrency}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            ≈ ${formatDualCurrencyForCard(viewingSale.totalCDF, settings?.tauxChangeUSDCDF || 2800).secondaryAmount}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Taux de change: 1 USD = {settings?.tauxChangeUSDCDF || 2800} CDF
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Méthode de paiement:</Typography>
                <Chip
                  icon={getPaymentMethodIcon(viewingSale.methodePaiement)}
                  label={getPaymentMethodLabel(viewingSale.methodePaiement)}
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2">Type de vente:</Typography>
                <Chip
                  label={viewingSale.typeVente === 'cash' ? 'Cash' : 'Crédit'}
                  color={viewingSale.typeVente === 'cash' ? 'success' : 'warning'}
                />
              </Grid>
              {viewingSale.notes && (
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Notes:</Typography>
                  <Typography variant="body1">{viewingSale.notes}</Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenViewDialog(false)}>Fermer</Button>
        </DialogActions>
      </Dialog>

      {/* Receipt Preview Modal */}
      <ReceiptPreviewModal
        open={receiptPreviewOpen}
        onClose={() => setReceiptPreviewOpen(false)}
        receiptData={receiptData}
        onPrintSuccess={() => {
          setSuccess('Reçu imprimé avec succès');
          setTimeout(() => setSuccess(''), 3000);
        }}
      />
    </Box>
  );
};

export default SalesPage;
