/**
 * Revenue Dashboard Component for Super Admin
 * Displays comprehensive revenue analytics and profit margins
 */

import React from 'react';
import {
  Box,
  Card,
  CardContent,
  CardHeader,
  Grid,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  LinearProgress,
  Alert
} from '@mui/material';
import {
  TrendingUp,
  Category,
  Inventory,
  AttachMoney
} from '@mui/icons-material';

import { Product, Sale } from '@/types';
import { RevenueAnalytics, formatRevenue } from '@/utils/revenue';
import { formatDualCurrencyForCard } from '@/utils';

interface RevenueDashboardProps {
  analytics: RevenueAnalytics;
  products: Product[];
  isLoading?: boolean;
}

export const RevenueDashboard: React.FC<RevenueDashboardProps> = ({
  analytics,
  products,
  isLoading = false
}) => {
  if (isLoading) {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Revenus - Analyse des Bénéfices
        </Typography>
        <LinearProgress />
      </Box>
    );
  }

  if (analytics.totalProductsWithProfit === 0) {
    return (
      <Box>
        <Typography variant="h5" gutterBottom>
          Revenus - Analyse des Bénéfices
        </Typography>
        <Alert severity="info">
          Aucun produit avec prix d'achat et de vente configurés. 
          Ajoutez des prix d'achat aux produits pour voir l'analyse des revenus.
        </Alert>
      </Box>
    );
  }

  const categoryEntries = Object.entries(analytics.categoryBreakdown);

  return (
    <Box>
      <Typography variant="h5" gutterBottom sx={{ mb: 3 }}>
        Revenus - Analyse des Bénéfices
      </Typography>

      {/* Revenue Summary Cards */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Revenus Potentiels
                  </Typography>
                  <Typography variant="h6" fontWeight="medium">
                    {formatDualCurrencyForCard(analytics.totalInventoryRevenueCDF, 2800).primaryAmount} {formatDualCurrencyForCard(analytics.totalInventoryRevenueCDF, 2800).primaryCurrency}
                  </Typography>
                  <Typography variant="body2" color="primary">
                    ≈ ${formatDualCurrencyForCard(analytics.totalInventoryRevenueCDF, 2800).secondaryAmount}
                  </Typography>
                </Box>
                <Inventory color="primary" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Revenus Réalisés
                  </Typography>
                  <Typography variant="h6" fontWeight="medium">
                    {formatDualCurrencyForCard(analytics.realizedRevenueCDF, 2800).primaryAmount} {formatDualCurrencyForCard(analytics.realizedRevenueCDF, 2800).primaryCurrency}
                  </Typography>
                  <Typography variant="body2" color="success.main">
                    ≈ ${formatDualCurrencyForCard(analytics.realizedRevenueCDF, 2800).secondaryAmount}
                  </Typography>
                </Box>
                <AttachMoney color="success" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>



        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Catégories
                  </Typography>
                  <Typography variant="h6">
                    {categoryEntries.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    avec revenus
                  </Typography>
                </Box>
                <Category color="info" sx={{ fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Top Performing Products */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Produits les Plus Rentables" />
            <CardContent>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Produit</TableCell>
                      <TableCell align="right">Revenus Potentiels</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {analytics.topProducts.map((product) => {
                      const totalProfit = (product.beneficeUnitaireCDF || 0) * product.stock;

                      return (
                        <TableRow key={product.id}>
                          <TableCell>
                            <Box>
                              <Typography variant="body2" fontWeight="medium">
                                {product.nom}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                Stock: {product.stock}
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2">
                              {formatRevenue(totalProfit, 'CDF')}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>

        {/* Revenue by Category */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardHeader title="Revenus par Catégorie" />
            <CardContent>
              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Catégorie</TableCell>
                      <TableCell align="right">Produits</TableCell>
                      <TableCell align="right">Revenus (CDF)</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {categoryEntries
                      .sort(([,a], [,b]) => b.revenueCDF - a.revenueCDF)
                      .map(([category, data]) => (
                        <TableRow key={category}>
                          <TableCell>
                            <Chip label={category} size="small" />
                          </TableCell>
                          <TableCell align="right">
                            {data.productCount}
                          </TableCell>
                          <TableCell align="right">
                            <Typography variant="body2">
                              {formatRevenue(data.revenueCDF, 'CDF')}
                            </Typography>
                            <Typography variant="caption" color="textSecondary">
                              {formatRevenue(data.revenueUSD, 'USD')}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};
