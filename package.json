{"name": "smartboutique", "version": "1.1.0", "description": "A fully functional modern desktop application for managing retail business operations.", "main": "dist-electron/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on http://localhost:5173 && npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "electron .", "build": "npm run build:vite && npm run build:electron", "build:vite": "vite build", "build:electron": "tsc -p electron", "build:production": "node scripts/build-production.js", "dist": "npm run build && electron-builder", "dist:win": "npm run build && electron-builder --win", "dist:portable": "npm run build && electron-builder --win portable", "dist:client": "npm run build:production && npm run dist:win && npm run dist:portable", "pack": "npm run build && electron-builder --dir", "preview": "vite preview", "postinstall": "electron-builder install-app-deps", "mobile:build": "npm run build:vite && npx cap sync", "mobile:dev": "npm run build:vite && npx cap run android", "mobile:dev:ios": "npm run build:vite && npx cap run ios", "mobile:open:android": "npx cap open android", "mobile:open:ios": "npx cap open ios", "mobile:sync": "npx cap sync", "mobile:add:android": "npx cap add android", "mobile:add:ios": "npx cap add ios", "test:mobile": "vite --host 0.0.0.0 --port 5173"}, "keywords": ["retail", "management", "electron", "react", "typescript", "boutique"], "author": "SmartBoutique Team", "license": "MIT", "build": {"appId": "com.smartboutique.app", "productName": "SmartBoutique", "copyright": "Copyright © 2024 SmartBoutique Team", "directories": {"output": "release-final", "buildResources": "build"}, "files": ["dist/**/*", "dist-electron/**/*", "node_modules/**/*", "!node_modules/**/*.{md,txt,LICENSE,CHANGELOG,README}", "!node_modules/**/test/**/*", "!node_modules/**/*.d.ts", "!node_modules/**/docs/**/*", "!node_modules/**/example/**/*", "!node_modules/**/examples/**/*", "!node_modules/**/*.map", "node_modules/better-sqlite3/**/*"], "extraResources": [{"from": "assets", "to": "assets", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "publisherName": "SmartBoutique Team", "verifyUpdateCodeSignature": false, "requestedExecutionLevel": "asInvoker", "artifactName": "SmartBoutique-Setup-${version}.${ext}"}, "nsis": {"oneClick": false, "perMachine": false, "allowToChangeInstallationDirectory": true, "deleteAppDataOnUninstall": false, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "SmartBoutique", "license": "build/license.txt", "language": "1036", "displayLanguageSelector": false, "artifactName": "SmartBoutique-Installer-${version}.${ext}"}, "portable": {"artifactName": "SmartBoutique-Portable-${version}.${ext}"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}, "extraMetadata": {"main": "dist-electron/main.js"}, "nodeGypRebuild": false, "buildDependenciesFromSource": false}, "dependencies": {"@capacitor-community/sqlite": "^7.0.0", "@capacitor/android": "^7.3.0", "@capacitor/camera": "^7.0.1", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/filesystem": "^7.1.1", "@capacitor/ios": "^7.3.0", "@capacitor/preferences": "^7.0.1", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^5.16.10", "@mui/material": "^5.16.10", "better-sqlite3": "^11.10.0", "chart.js": "^4.4.7", "date-fns": "^3.6.0", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-router-dom": "^6.29.0"}, "devDependencies": {"@types/better-sqlite3": "^7.6.13", "@types/node": "^20.19.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@vitejs/plugin-react": "^4.5.2", "concurrently": "^8.2.2", "electron": "^28.3.3", "electron-builder": "^24.13.3", "terser": "^5.43.1", "typescript": "^5.8.3", "vite": "^5.4.19", "wait-on": "^7.2.0"}}