// User types
export interface User {
  id: string;
  nom: string;
  email: string;
  role: 'employee' | 'admin' | 'super_admin';
  motDePasse: string;
  dateCreation: string;
  actif: boolean;
}

// Product types
export interface Product {
  id: string;
  nom: string;
  description: string;
  prixAchatCDF: number; // Purchase price in CDF
  prixAchatUSD?: number; // Purchase price in USD (calculated)
  prixCDF: number; // Selling price in CDF
  prixUSD?: number; // Selling price in USD (calculated)
  beneficeUnitaireCDF?: number; // Profit margin in CDF (calculated)
  beneficeUnitaireUSD?: number; // Profit margin in USD (calculated)
  codeQR: string;
  categorie: string;
  stock: number;
  stockMin: number;
  codeBarres: string;
  dateCreation: string;
  dateModification: string;
  // Enhanced inventory management fields
  quantiteEnStock?: number; // Editable stock quantity (can be different from stock for manual adjustments)
  coutAchatStockCDF?: number; // Total cost of current stock inventory in CDF
  coutAchatStockUSD?: number; // Total cost of current stock inventory in USD
  prixParPieceCDF?: number; // Unit price for retail sales in CDF (can be different from prixCDF)
  prixParPieceUSD?: number; // Unit price for retail sales in USD
}

// Category types
export interface Category {
  id: string;
  nom: string;
  description: string;
  couleur: string;
}

// Sale types
export interface Sale {
  id: string;
  produits: SaleItem[];
  nomClient: string;
  telephoneClient?: string;
  adresseClient?: string;
  totalCDF: number;
  totalUSD?: number;
  methodePaiement: 'cash' | 'card' | 'mobile_money';
  typeVente: 'cash' | 'credit';
  datevente: string;
  vendeur: string;
  notes?: string;
  numeroRecu?: string; // Receipt number
}

export interface SaleItem {
  produitId: string;
  nomProduit: string;
  quantite: number;
  prixUnitaireCDF: number;
  prixUnitaireUSD?: number;
  totalCDF: number;
  totalUSD?: number;
}

// Debt types
export interface Debt {
  id: string;
  venteId: string;
  nomClient: string;
  telephoneClient?: string;
  adresseClient?: string;
  montantTotalCDF: number;
  montantTotalUSD?: number;
  montantPayeCDF: number;
  montantPayeUSD?: number;
  montantRestantCDF: number;
  montantRestantUSD?: number;
  dateCreation: string;
  dateEcheance: string;
  statut: 'active' | 'overdue' | 'paid';
  statutPaiement: 'paye' | 'impaye'; // Manual payment status
  paiements: Payment[];
  notes?: string;
  deviseVente?: 'CDF' | 'USD'; // Primary currency for the sale/debt
}

export interface Payment {
  id: string;
  montantCDF: number;
  montantUSD?: number;
  methodePaiement: 'cash' | 'banque' | 'mobile_money';
  datePaiement: string;
  notes?: string;
  deviseOriginale?: 'CDF' | 'USD'; // Track the original currency used for this payment
}

// Expense types
export interface Expense {
  id: string;
  description: string;
  montantCDF: number;
  montantUSD?: number;
  categorie: string;
  dateDepense: string;
  notes?: string;
  creePar: string;
  numeroRecu?: string; // Receipt number
}

// Settings types
export interface Settings {
  tauxChangeUSDCDF: number;
  seuilStockBas: number;
  categories: Category[];
  entreprise: {
    nom: string;
    adresse: string;
    telephone: string;
    email: string;
    rccm?: string; // Registre de Commerce et du Crédit Mobilier
    idNat?: string; // Identification Nationale
    logo?: string; // Base64 encoded logo image data
  };
  impression?: {
    impressionAutomatique: boolean;
    taillePapier: 'thermal' | 'a4';
  };
}

// Dashboard types
export type TimePeriod = 'jour' | 'semaine' | 'mois';

export interface DashboardStats {
  ventesDuJour: {
    nombreVentes: number;
    revenusCDF: number;
    revenusUSD?: number;
  };
  ventesDeLaSemaine: {
    nombreVentes: number;
    revenusCDF: number;
    revenusUSD?: number;
  };
  ventesDuMois: {
    nombreVentes: number;
    revenusCDF: number;
    revenusUSD?: number;
  };
  // Renamed from produitsActifs to articlesActifs for clarity
  articlesActifs: number;
  produitsStockBas: number;
  // New field for out-of-stock products
  articlesEnRupture: number;
  valeurInventaireCDF: number;
  valeurInventaireUSD?: number;
  dettesActives: number;
  dettesEnRetard: number;
  montantDettesTotalCDF: number;
  montantDettesTotalUSD?: number;
}

// Chart data types
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// Filter types
export interface ProductFilter {
  nom?: string;
  categorie?: string;
  stockStatus?: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
}

export interface SaleFilter {
  dateDebut?: string;
  dateFin?: string;
  nomClient?: string;
  vendeur?: string;
  methodePaiement?: string;
}

export interface DebtFilter {
  statut?: 'all' | 'active' | 'overdue' | 'paid';
  nomClient?: string;
  dateDebut?: string;
  dateFin?: string;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  titre: string;
  message: string;
  dateCreation: string;
  lu: boolean;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Form types
export interface ProductForm {
  nom: string;
  description: string;
  prixAchatCDF: number; // Purchase price
  prixCDF: number; // Selling price
  categorie: string;
  stock: number;
  stockMin: number;
  // Enhanced inventory management fields
  quantiteEnStock: number; // Editable stock quantity
  coutAchatStockCDF: number; // Total cost of current stock inventory in CDF
  prixParPieceCDF: number; // Unit price for retail sales in CDF
}

export interface SaleForm {
  produits: SaleItemForm[];
  nomClient: string;
  telephoneClient?: string;
  adresseClient?: string;
  methodePaiement: 'cash' | 'card' | 'mobile_money';
  typeVente: 'cash' | 'credit';
  dateEcheance?: string;
  notes?: string;
}

export interface SaleItemForm {
  produitId: string;
  quantite: number;
}

export interface UserForm {
  nom: string;
  email: string;
  role: 'employee' | 'admin' | 'super_admin';
  motDePasse: string;
}

// Receipt types
export interface ReceiptData {
  type: 'sale' | 'expense';
  numero: string;
  date: string;
  entreprise: {
    nom: string;
    adresse: string;
    telephone: string;
    rccm?: string; // Registre de Commerce et du Crédit Mobilier
    idNat?: string; // Identification Nationale
    logo?: string; // Base64 encoded logo image data
  };
}

export interface SalesReceiptData extends ReceiptData {
  type: 'sale';
  sale: Sale;
  vendeur: string;
}

export interface ExpenseReceiptData extends ReceiptData {
  type: 'expense';
  expense: Expense;
  creePar: string;
}

// Stock status type
export type StockStatus = 'in_stock' | 'low_stock' | 'out_of_stock';

// Employee types
export interface Employee {
  id: string;
  nom: string;
  prenom?: string;
  poste: string;
  salaireCDF: number;
  salaireUSD?: number;
  dateEmbauche: string;
  telephone?: string;
  adresse?: string;
  statut: 'actif' | 'inactif' | 'suspendu';
  notes?: string;
  creePar: string;
  dateCreation: string;
  dateModification?: string;
}

export interface EmployeeForm {
  nom: string;
  prenom?: string;
  poste: string;
  salaireCDF: number;
  dateEmbauche: string;
  telephone?: string;
  adresse?: string;
  statut: 'actif' | 'inactif' | 'suspendu';
  notes?: string;
}

// Employee Payment types
export interface EmployeePayment {
  id: string;
  nomEmploye: string;
  montantCDF: number;
  montantUSD?: number;
  datePaiement: string;
  methodePaiement: 'cash' | 'mobile_money' | 'bank';
  notes?: string;
  creePar: string; // User who created the payment record
  dateCreation: string;
  dateModification?: string;
}

export interface EmployeePaymentForm {
  nomEmploye: string;
  montantCDF: number;
  datePaiement: string;
  methodePaiement: 'cash' | 'mobile_money' | 'bank';
  notes?: string;
}

export interface EmployeePaymentFilter {
  nomEmploye?: string;
  dateDebut?: string;
  dateFin?: string;
  methodePaiement?: string;
}

// Permission types
export interface Permission {
  canViewDashboard: boolean;
  canViewProducts: boolean;
  canManageProducts: boolean;
  canViewSales: boolean;
  canManageSales: boolean;
  canViewDebts: boolean;
  canManageDebts: boolean;
  canViewReports: boolean;
  canViewUsers: boolean;
  canManageUsers: boolean;
  canViewSettings: boolean;
  canManageSettings: boolean;
  canViewExpenses: boolean;
  canManageExpenses: boolean;
  canViewFinancials: boolean;
  canViewRevenue: boolean; // Super Admin only - revenue and profit analytics
  canViewEmployeePayments: boolean; // Admin only - employee payment tracking
  canManageEmployeePayments: boolean; // Admin only - manage employee payments
}
