// Utility functions for the SmartBoutique application

import { Product, StockStatus } from '@/types';

// Re-export CSV utilities for backward compatibility
export { CSVUtils } from './csv';
export { PRODUCT_COLUMNS } from './csv-columns';

/**
 * Format currency values with CDF as primary (legacy function - use formatDualCurrency for new implementations)
 */
export const formatCurrency = (amountCDF: number, amountUSD?: number): string => {
  // Handle undefined, null, or NaN values
  const safeCDF = (amountCDF == null || isNaN(amountCDF)) ? 0 : amountCDF;
  const cdfFormatted = `${safeCDF.toLocaleString('fr-FR')} CDF`;

  if (amountUSD && !isNaN(amountUSD)) {
    const usdFormatted = `$${amountUSD.toLocaleString('fr-FR', { minimumFractionDigits: 2 })}`;
    return `${cdfFormatted} (≈ ${usdFormatted})`;
  }
  return cdfFormatted;
};

/**
 * Format currency for single currency display
 */
export const formatSingleCurrency = (amount: number, currency: 'USD' | 'CDF'): string => {
  // Handle undefined, null, or NaN values
  const safeAmount = (amount == null || isNaN(amount)) ? 0 : amount;

  if (currency === 'USD') {
    return `$${safeAmount.toLocaleString('fr-FR', { minimumFractionDigits: 2 })}`;
  }
  return `${safeAmount.toLocaleString('fr-FR')} CDF`;
};

/**
 * Enhanced dual currency formatting for dashboard displays
 * Shows CDF as primary with USD as secondary in a consistent format
 */
export const formatDualCurrency = (
  amountCDF: number,
  exchangeRate: number = 2800,
  options?: {
    showSymbols?: boolean;
    compact?: boolean;
    primaryOnly?: boolean;
  }
): { primary: string; secondary: string; combined: string } => {
  // Handle undefined, null, or NaN values
  const safeCDF = (amountCDF == null || isNaN(amountCDF)) ? 0 : amountCDF;
  const safeRate = (exchangeRate == null || isNaN(exchangeRate) || exchangeRate <= 0) ? 2800 : exchangeRate;

  // Calculate USD equivalent
  const amountUSD = safeCDF / safeRate;

  // Format options
  const showSymbols = options?.showSymbols ?? true;
  const compact = options?.compact ?? false;
  const primaryOnly = options?.primaryOnly ?? false;

  // Format CDF (primary currency)
  const cdfFormatted = showSymbols
    ? `${safeCDF.toLocaleString('fr-FR')} CDF`
    : safeCDF.toLocaleString('fr-FR');

  // Format USD (secondary currency)
  const usdFormatted = showSymbols
    ? `$${amountUSD.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    : amountUSD.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 });

  // Return structured format
  if (primaryOnly) {
    return {
      primary: cdfFormatted,
      secondary: '',
      combined: cdfFormatted
    };
  }

  const combined = compact
    ? `${cdfFormatted} / ${usdFormatted}`
    : `${cdfFormatted} (≈ ${usdFormatted})`;

  return {
    primary: cdfFormatted,
    secondary: usdFormatted,
    combined
  };
};

/**
 * Format dual currency for card displays with proper styling structure
 */
export const formatDualCurrencyForCard = (
  amountCDF: number,
  exchangeRate: number = 2800
): { primaryAmount: string; secondaryAmount: string; primaryCurrency: string; secondaryCurrency: string } => {
  const safeCDF = (amountCDF == null || isNaN(amountCDF)) ? 0 : amountCDF;
  const safeRate = (exchangeRate == null || isNaN(exchangeRate) || exchangeRate <= 0) ? 2800 : exchangeRate;
  const amountUSD = safeCDF / safeRate;

  return {
    primaryAmount: safeCDF.toLocaleString('fr-FR'),
    secondaryAmount: amountUSD.toLocaleString('fr-FR', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
    primaryCurrency: 'CDF',
    secondaryCurrency: 'USD'
  };
};

/**
 * Format dual currency for table cells with consistent alignment
 */
export const formatDualCurrencyForTable = (
  amountCDF: number,
  exchangeRate: number = 2800,
  options?: { showBoth?: boolean; primaryFirst?: boolean }
): string => {
  const showBoth = options?.showBoth ?? true;
  const primaryFirst = options?.primaryFirst ?? true;

  if (!showBoth) {
    return formatSingleCurrency(amountCDF, 'CDF');
  }

  const { primary, secondary } = formatDualCurrency(amountCDF, exchangeRate);

  return primaryFirst ? `${primary}\n${secondary}` : `${secondary}\n${primary}`;
};

/**
 * Get stock status based on current stock and minimum stock
 */
export const getStockStatus = (product: Product): StockStatus => {
  if (product.stock === 0) return 'out_of_stock';
  if (product.stock <= product.stockMin) return 'low_stock';
  return 'in_stock';
};

/**
 * Generate a simple barcode (EAN-13 format simulation)
 */
export const generateBarcode = (): string => {
  const prefix = '123'; // Country code simulation
  const timestamp = Date.now().toString().slice(-10);
  return prefix + timestamp;
};

/**
 * Generate QR-compatible code for products
 */
export const generateQRCode = (): string => {
  const prefix = 'SB'; // SmartBoutique prefix
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 4).toUpperCase();
  return `${prefix}${timestamp.slice(-8)}${random}`;
};

/**
 * Validate email format
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Generate a unique ID
 */
export const generateId = (prefix?: string): string => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substr(2, 5);
  return prefix ? `${prefix}-${timestamp}-${random}` : `${timestamp}-${random}`;
};

/**
 * Calculate percentage
 */
export const calculatePercentage = (value: number, total: number): number => {
  if (total === 0) return 0;
  return (value / total) * 100;
};

/**
 * Truncate text to specified length
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substr(0, maxLength) + '...';
};

/**
 * Convert CDF to USD using exchange rate (CDF is now primary)
 */
export const convertCDFToUSD = (amountCDF: number, exchangeRate: number): number => {
  return amountCDF / exchangeRate;
};

/**
 * Convert USD to CDF using exchange rate
 */
export const convertUSDToCDF = (amountUSD: number, exchangeRate: number): number => {
  return amountUSD * exchangeRate;
};

/**
 * Calculate USD equivalent from CDF amount
 */
export const calculateUSDFromCDF = (amountCDF: number, exchangeRate: number): number => {
  return Math.round((amountCDF / exchangeRate) * 100) / 100; // Round to 2 decimal places
};

/**
 * Check if a product needs low stock notification
 */
export const needsLowStockNotification = (product: Product, threshold: number = 5): boolean => {
  return product.stock > 0 && product.stock <= threshold;
};

/**
 * Get products that need low stock notifications
 */
export const getLowStockProducts = (products: Product[], threshold: number = 5): Product[] => {
  return products.filter(product => needsLowStockNotification(product, threshold));
};

/**
 * Calculate suggested reorder quantity for a product
 */
export const calculateSuggestedReorder = (product: Product): number => {
  // Suggest reordering to reach 3x minimum stock or 20 units, whichever is higher
  const suggestedStock = Math.max(product.stockMin * 3, 20);
  return Math.max(0, suggestedStock - product.stock);
};

/**
 * Format date for display
 */
export const formatDate = (date: Date | string, format: 'short' | 'long' | 'datetime' = 'short'): string => {
  const d = typeof date === 'string' ? new Date(date) : date;
  
  switch (format) {
    case 'short':
      return d.toLocaleDateString('fr-FR');
    case 'long':
      return d.toLocaleDateString('fr-FR', { 
        weekday: 'long', 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
      });
    case 'datetime':
      return d.toLocaleString('fr-FR');
    default:
      return d.toLocaleDateString('fr-FR');
  }
};

/**
 * Debounce function for search inputs
 */
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
};

/**
 * Deep clone an object
 */
export const deepClone = <T>(obj: T): T => {
  return JSON.parse(JSON.stringify(obj));
};

/**
 * Check if a value is empty (null, undefined, empty string, empty array, empty object)
 */
export const isEmpty = (value: any): boolean => {
  if (value === null || value === undefined) return true;
  if (typeof value === 'string') return value.trim() === '';
  if (Array.isArray(value)) return value.length === 0;
  if (typeof value === 'object') return Object.keys(value).length === 0;
  return false;
};

/**
 * Capitalize first letter of a string
 */
export const capitalize = (str: string): string => {
  if (!str) return '';
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Generate CSV content from array of objects
 */
export const generateCSV = (data: any[], headers: string[]): string => {
  const csvHeaders = headers.join(',');
  const csvRows = data.map(row => 
    headers.map(header => {
      const value = row[header];
      // Escape commas and quotes in CSV
      if (typeof value === 'string' && (value.includes(',') || value.includes('"'))) {
        return `"${value.replace(/"/g, '""')}"`;
      }
      return value;
    }).join(',')
  );
  
  return [csvHeaders, ...csvRows].join('\n');
};

/**
 * Download a file with given content
 */
export const downloadFile = (content: string, filename: string, contentType: string = 'text/plain'): void => {
  const blob = new Blob([content], { type: contentType });
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = filename;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
};

/**
 * Parse CSV content to array of objects
 */
export const parseCSV = (csvContent: string, headers: string[]): any[] => {
  const lines = csvContent.split('\n').filter(line => line.trim());
  const dataLines = lines.slice(1); // Skip header line
  
  return dataLines.map(line => {
    const values = line.split(',').map(value => value.trim().replace(/^"|"$/g, ''));
    const obj: any = {};
    headers.forEach((header, index) => {
      obj[header] = values[index] || '';
    });
    return obj;
  });
};

/**
 * Validate required fields in an object
 */
export const validateRequiredFields = (obj: any, requiredFields: string[]): string[] => {
  const missingFields: string[] = [];
  
  requiredFields.forEach(field => {
    if (isEmpty(obj[field])) {
      missingFields.push(field);
    }
  });
  
  return missingFields;
};

/**
 * Sort array of objects by a specific field
 */
export const sortByField = <T>(array: T[], field: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {
  return [...array].sort((a, b) => {
    const aValue = a[field];
    const bValue = b[field];
    
    if (aValue < bValue) return direction === 'asc' ? -1 : 1;
    if (aValue > bValue) return direction === 'asc' ? 1 : -1;
    return 0;
  });
};

/**
 * Filter array of objects by search term in multiple fields
 */
export const searchInFields = <T>(
  array: T[], 
  searchTerm: string, 
  fields: (keyof T)[]
): T[] => {
  if (!searchTerm.trim()) return array;
  
  const term = searchTerm.toLowerCase();
  return array.filter(item =>
    fields.some(field => {
      const value = item[field];
      return typeof value === 'string' && value.toLowerCase().includes(term);
    })
  );
};

/**
 * Get color based on stock status
 */
export const getStockStatusColor = (status: StockStatus): string => {
  switch (status) {
    case 'out_of_stock': return '#f44336'; // red
    case 'low_stock': return '#ff9800'; // orange
    case 'in_stock': return '#4caf50'; // green
    default: return '#9e9e9e'; // grey
  }
};

/**
 * Format phone number
 */
export const formatPhoneNumber = (phone: string): string => {
  // Remove all non-digit characters
  const cleaned = phone.replace(/\D/g, '');
  
  // Format as +243 XXX XXX XXX for DRC numbers
  if (cleaned.startsWith('243') && cleaned.length === 12) {
    return `+${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6, 9)} ${cleaned.slice(9)}`;
  }
  
  // Return original if doesn't match expected format
  return phone;
};

/**
 * Calculate age from date of birth
 */
export const calculateAge = (dateOfBirth: Date | string): number => {
  const birth = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
  const today = new Date();
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
};
